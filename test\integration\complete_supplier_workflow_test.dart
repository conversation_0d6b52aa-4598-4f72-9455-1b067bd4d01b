import 'package:flutter_test/flutter_test.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';
import 'package:market/features/suppliers/domain/entities/supplier_account.dart';
import 'package:market/features/suppliers/domain/usecases/add_supplier_account_entry.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_account_statement.dart';
import 'package:market/features/suppliers/data/repositories/supplier_account_repository_impl.dart';
import 'package:market/features/suppliers/data/datasources/supplier_account_database_service.dart';
import 'package:market/core/database/database_service.dart';

void main() {
  group('Complete Supplier Workflow Integration Tests', () {
    late DatabaseService databaseService;
    late SupplierAccountDatabaseService dataSource;
    late SupplierAccountRepositoryImpl repository;
    late AddSupplierAccountEntryUseCase addEntryUseCase;
    late GetSupplierAccountStatementUseCase getStatementUseCase;

    setUpAll(() async {
      // Initialize database service
      databaseService = DatabaseService.instance;
      await databaseService.database;

      // Initialize components
      dataSource = SupplierAccountDatabaseService(databaseService);
      repository = SupplierAccountRepositoryImpl(dataSource);
      addEntryUseCase = AddSupplierAccountEntryUseCase(repository);
      getStatementUseCase = GetSupplierAccountStatementUseCase(repository);
    });

    tearDown(() async {
      // Clean up test data
      final db = await databaseService.database;
      await db.delete('supplier_accounts');
    });

    test('should handle complete supplier workflow: purchase -> payment -> statement', () async {
      const supplierId = 1;
      
      // Step 1: Create a purchase invoice
      final purchaseEntry = SupplierAccount(
        supplierId: supplierId,
        transactionDate: DateTime.now().subtract(const Duration(days: 2)),
        type: 'purchase_invoice',
        amount: 2000.0,
        description: 'فاتورة شراء رقم #1001',
        relatedInvoiceId: 1001,
      );

      await addEntryUseCase.call(purchaseEntry);

      // Step 2: Make a partial payment
      final paymentEntry = SupplierAccount(
        supplierId: supplierId,
        transactionDate: DateTime.now().subtract(const Duration(days: 1)),
        type: 'payment_out',
        amount: 800.0,
        description: 'سند دفع جزئي للمورد',
      );

      await addEntryUseCase.call(paymentEntry);

      // Step 3: Create another purchase
      final secondPurchaseEntry = SupplierAccount(
        supplierId: supplierId,
        transactionDate: DateTime.now(),
        type: 'purchase_invoice',
        amount: 1500.0,
        description: 'فاتورة شراء رقم #1002',
        relatedInvoiceId: 1002,
      );

      await addEntryUseCase.call(secondPurchaseEntry);

      // Step 4: Get complete statement
      final statement = await getStatementUseCase.call(supplierId);

      // Verify statement structure
      expect(statement, hasLength(3));

      // Verify chronological order (oldest first)
      expect(statement[0].type, 'purchase_invoice');
      expect(statement[0].amount, 2000.0);
      expect(statement[1].type, 'payment_out');
      expect(statement[1].amount, 800.0);
      expect(statement[2].type, 'purchase_invoice');
      expect(statement[2].amount, 1500.0);

      // Verify running balance calculation
      // After first purchase: +2000
      // After payment: +2000 - 800 = +1200
      // After second purchase: +1200 + 1500 = +2700
      
      // Calculate running balance manually to verify
      double runningBalance = 0.0;
      for (int i = 0; i < statement.length; i++) {
        final transaction = statement[i];
        if (transaction.isDebit) {
          runningBalance += transaction.amount;
        } else if (transaction.isCredit) {
          runningBalance -= transaction.amount;
        }
        
        // Verify the balance progression
        switch (i) {
          case 0: // First purchase
            expect(runningBalance, 2000.0);
            break;
          case 1: // After payment
            expect(runningBalance, 1200.0);
            break;
          case 2: // Second purchase
            expect(runningBalance, 2700.0);
            break;
        }
      }

      // Final balance should be 2700 (total purchases 3500 - total payments 800)
      expect(runningBalance, 2700.0);
    });

    test('should handle multiple suppliers independently', () async {
      // Create entries for supplier 1
      await addEntryUseCase.call(SupplierAccount(
        supplierId: 1,
        transactionDate: DateTime.now(),
        type: 'purchase_invoice',
        amount: 1000.0,
        description: 'Purchase for supplier 1',
      ));

      // Create entries for supplier 2
      await addEntryUseCase.call(SupplierAccount(
        supplierId: 2,
        transactionDate: DateTime.now(),
        type: 'purchase_invoice',
        amount: 2000.0,
        description: 'Purchase for supplier 2',
      ));

      // Get statements for each supplier
      final statement1 = await getStatementUseCase.call(1);
      final statement2 = await getStatementUseCase.call(2);

      // Verify isolation
      expect(statement1, hasLength(1));
      expect(statement2, hasLength(1));
      expect(statement1.first.amount, 1000.0);
      expect(statement2.first.amount, 2000.0);
    });

    test('should handle edge cases correctly', () async {
      const supplierId = 3;

      // Test with zero amount (should fail validation)
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: supplierId,
          transactionDate: DateTime.now(),
          type: 'purchase_invoice',
          amount: 0.0,
        )),
        throwsException,
      );

      // Test with negative amount (should fail validation)
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: supplierId,
          transactionDate: DateTime.now(),
          type: 'purchase_invoice',
          amount: -100.0,
        )),
        throwsException,
      );

      // Test with invalid supplier ID
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: 0,
          transactionDate: DateTime.now(),
          type: 'purchase_invoice',
          amount: 100.0,
        )),
        throwsException,
      );

      // Test getting statement for non-existent supplier
      final emptyStatement = await getStatementUseCase.call(999);
      expect(emptyStatement, isEmpty);
    });
  });
}
