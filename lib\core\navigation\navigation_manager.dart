import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';

class NavigationManager {
  static final NavigationManager _instance = NavigationManager._();
  
  NavigationManager._();
  
  factory NavigationManager() => _instance;
  
  static NavigationManager get instance => _instance;

  BuildContext? get _context {
    final navigatorKey = GetIt.instance<GlobalKey<NavigatorState>>();
    return navigatorKey.currentContext;
  }

  bool canPop() {
    final context = _context;
    if (context == null) return false;
    return GoRouter.of(context).canPop();
  }

  void pop() {
    final context = _context;
    if (context == null) return;
    GoRouter.of(context).pop();
  }

  String get currentRoute {
    final context = _context;
    if (context == null) return '/';
    return GoRouterState.of(context).fullPath ?? '/';
  }

  void goTo(String route) {
    final context = _context;
    if (context == null) return;
    GoRouter.of(context).go(route);
  }
}
