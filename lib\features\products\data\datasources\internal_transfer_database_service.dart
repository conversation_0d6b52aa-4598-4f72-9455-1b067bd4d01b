import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/internal_transfer_model.dart';

class InternalTransferDatabaseService {
  final DatabaseService _databaseService;

  InternalTransferDatabaseService(this._databaseService);

  /// Add a new internal transfer to the database
  Future<int> addTransfer(InternalTransferModel transfer) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(
        'internal_transfers',
        transfer.toMap()..remove('id'), // Remove id for auto-increment
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e) {
      throw Exception('Failed to add internal transfer: $e');
    }
  }

  /// Get all internal transfers
  Future<List<InternalTransferModel>> getTransfers() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'internal_transfers',
        orderBy: 'transferDate DESC, id DESC', // Most recent first
      );

      return List.generate(maps.length, (i) {
        return InternalTransferModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get internal transfers: $e');
    }
  }

  /// Get a specific transfer by ID
  Future<InternalTransferModel?> getTransferById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'internal_transfers',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return InternalTransferModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get transfer by id: $e');
    }
  }

  /// Get all transfers for a specific product
  Future<List<InternalTransferModel>> getTransfersByProductId(int productId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'internal_transfers',
        where: 'productId = ?',
        whereArgs: [productId],
        orderBy: 'transferDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return InternalTransferModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get transfers for product: $e');
    }
  }

  /// Get transfers within a date range
  Future<List<InternalTransferModel>> getTransfersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'internal_transfers',
        where: 'transferDate BETWEEN ? AND ?',
        whereArgs: [
          startDate.toIso8601String(),
          endDate.toIso8601String(),
        ],
        orderBy: 'transferDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return InternalTransferModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get transfers by date range: $e');
    }
  }

  /// Get total transfer statistics
  Future<Map<String, dynamic>> getTransferStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total transfers count
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM internal_transfers'
      );
      final totalCount = countResult.first['count'] as int;

      // Get total value and cost
      final valueResult = await db.rawQuery(
        'SELECT SUM(totalValue) as totalValue, SUM(costAtTransfer * quantity) as totalCost FROM internal_transfers'
      );
      final totalValue = (valueResult.first['totalValue'] as num?)?.toDouble() ?? 0.0;
      final totalCost = (valueResult.first['totalCost'] as num?)?.toDouble() ?? 0.0;

      // Get total quantity transferred
      final quantityResult = await db.rawQuery(
        'SELECT SUM(quantity) as totalQuantity FROM internal_transfers'
      );
      final totalQuantity = quantityResult.first['totalQuantity'] as int? ?? 0;

      return {
        'totalCount': totalCount,
        'totalValue': totalValue,
        'totalCost': totalCost,
        'totalQuantity': totalQuantity,
        'totalProfit': totalValue - totalCost,
      };
    } catch (e) {
      throw Exception('Failed to get transfer statistics: $e');
    }
  }

  /// Delete all transfers for a product (used when product is deleted)
  Future<void> deleteTransfersForProduct(int productId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'internal_transfers',
        where: 'productId = ?',
        whereArgs: [productId],
      );
    } catch (e) {
      throw Exception('Failed to delete transfers for product: $e');
    }
  }
}
