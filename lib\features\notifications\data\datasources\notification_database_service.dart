import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/app_notification_model.dart';

class NotificationDatabaseService {
  final DatabaseService _databaseService;

  NotificationDatabaseService(this._databaseService);

  /// Create a new notification
  Future<int> createNotification(AppNotificationModel notification) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(
        'app_notifications',
        notification.toMap()..remove('id'), // Remove id for auto-increment
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }

  /// Get all notifications
  Future<List<AppNotificationModel>> getNotifications({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'app_notifications',
        orderBy: 'date DESC, id DESC', // Most recent first
        limit: limit,
        offset: offset,
      );

      return List.generate(maps.length, (i) {
        return AppNotificationModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get notifications: $e');
    }
  }

  /// Get unread notifications count
  Future<int> getUnreadNotificationsCount() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_notifications WHERE isRead = 0'
      );
      return result.first['count'] as int;
    } catch (e) {
      throw Exception('Failed to get unread notifications count: $e');
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(int id) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'app_notifications',
        {'isRead': 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  /// Mark notification action as taken
  Future<void> markNotificationActionTaken(int id) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'app_notifications',
        {'isActionTaken': 1, 'isRead': 1}, // Also mark as read
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to mark notification action as taken: $e');
    }
  }

  /// Get notifications by type
  Future<List<AppNotificationModel>> getNotificationsByType(String type) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'app_notifications',
        where: 'type = ?',
        whereArgs: [type],
        orderBy: 'date DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return AppNotificationModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get notifications by type: $e');
    }
  }

  /// Get notifications for a specific entity
  Future<List<AppNotificationModel>> getNotificationsForEntity(
    String entityType,
    int entityId,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'app_notifications',
        where: 'relatedEntityType = ? AND relatedEntityId = ?',
        whereArgs: [entityType, entityId],
        orderBy: 'date DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return AppNotificationModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get notifications for entity: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'app_notifications',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  /// Delete old notifications (older than specified days)
  Future<void> deleteOldNotifications(int daysOld) async {
    try {
      final db = await _databaseService.database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      await db.delete(
        'app_notifications',
        where: 'date < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );
    } catch (e) {
      throw Exception('Failed to delete old notifications: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'app_notifications',
        {'isRead': 1},
        where: 'isRead = 0',
      );
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  /// Get notification statistics
  Future<Map<String, dynamic>> getNotificationStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total count
      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_notifications'
      );
      final totalCount = totalResult.first['count'] as int;

      // Get unread count
      final unreadResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_notifications WHERE isRead = 0'
      );
      final unreadCount = unreadResult.first['count'] as int;

      // Get action taken count
      final actionTakenResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_notifications WHERE isActionTaken = 1'
      );
      final actionTakenCount = actionTakenResult.first['count'] as int;

      // Get counts by type
      final typeResult = await db.rawQuery('''
        SELECT type, COUNT(*) as count 
        FROM app_notifications 
        GROUP BY type
      ''');

      final typeCounts = <String, int>{};
      for (final row in typeResult) {
        typeCounts[row['type'] as String] = row['count'] as int;
      }

      return {
        'totalCount': totalCount,
        'unreadCount': unreadCount,
        'actionTakenCount': actionTakenCount,
        'typeCounts': typeCounts,
      };
    } catch (e) {
      throw Exception('Failed to get notification statistics: $e');
    }
  }
}
