class InternalTransferModel {
  final int? id;
  final int productId;
  final DateTime transferDate;
  final int quantity;
  final double retailPriceAtTransfer;
  final double costAtTransfer;
  final double totalValue;

  const InternalTransferModel({
    this.id,
    required this.productId,
    required this.transferDate,
    required this.quantity,
    required this.retailPriceAtTransfer,
    required this.costAtTransfer,
    required this.totalValue,
  });

  // Convert from Map (from database)
  factory InternalTransferModel.fromMap(Map<String, dynamic> map) {
    return InternalTransferModel(
      id: map['id'] as int?,
      productId: map['productId'] as int,
      transferDate: DateTime.parse(map['transferDate'] as String),
      quantity: map['quantity'] as int,
      retailPriceAtTransfer: (map['retailPriceAtTransfer'] as num).toDouble(),
      costAtTransfer: (map['costAtTransfer'] as num).toDouble(),
      totalValue: (map['totalValue'] as num).toDouble(),
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'transferDate': transferDate.toIso8601String(),
      'quantity': quantity,
      'retailPriceAtTransfer': retailPriceAtTransfer,
      'costAtTransfer': costAtTransfer,
      'totalValue': totalValue,
    };
  }

  // Copy with method for updates
  InternalTransferModel copyWith({
    int? id,
    int? productId,
    DateTime? transferDate,
    int? quantity,
    double? retailPriceAtTransfer,
    double? costAtTransfer,
    double? totalValue,
  }) {
    return InternalTransferModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      transferDate: transferDate ?? this.transferDate,
      quantity: quantity ?? this.quantity,
      retailPriceAtTransfer: retailPriceAtTransfer ?? this.retailPriceAtTransfer,
      costAtTransfer: costAtTransfer ?? this.costAtTransfer,
      totalValue: totalValue ?? this.totalValue,
    );
  }

  @override
  String toString() {
    return 'InternalTransferModel(id: $id, productId: $productId, '
        'transferDate: $transferDate, quantity: $quantity, '
        'retailPriceAtTransfer: $retailPriceAtTransfer, costAtTransfer: $costAtTransfer, '
        'totalValue: $totalValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InternalTransferModel &&
        other.id == id &&
        other.productId == productId &&
        other.transferDate == transferDate &&
        other.quantity == quantity &&
        other.retailPriceAtTransfer == retailPriceAtTransfer &&
        other.costAtTransfer == costAtTransfer &&
        other.totalValue == totalValue;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        transferDate.hashCode ^
        quantity.hashCode ^
        retailPriceAtTransfer.hashCode ^
        costAtTransfer.hashCode ^
        totalValue.hashCode;
  }
}
