import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/expense_model.dart';

class ExpenseDatabaseService {
  final DatabaseService _databaseService;

  ExpenseDatabaseService(this._databaseService);

  Future<List<ExpenseModel>> getAllExpenses() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        orderBy: 'expense_date DESC',
      );

      return List.generate(maps.length, (i) {
        return ExpenseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get expenses: $e');
    }
  }

  Future<ExpenseModel?> getExpenseById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return ExpenseModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get expense by id: $e');
    }
  }

  Future<int> createExpense(ExpenseModel expense) async {
    try {
      final db = await _databaseService.database;
      final expenseMap = expense.toMap();
      expenseMap.remove('id'); // Remove id for auto-increment

      return await db.insert(
        'expenses',
        expenseMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to create expense: $e');
    }
  }

  Future<void> updateExpense(ExpenseModel expense) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'expenses',
        expense.toMap(),
        where: 'id = ?',
        whereArgs: [expense.id],
      );
    } catch (e) {
      throw Exception('Failed to update expense: $e');
    }
  }

  Future<void> deleteExpense(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'expenses',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to delete expense: $e');
    }
  }

  Future<List<ExpenseModel>> getExpensesByCategory(String category) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        where: 'category = ?',
        whereArgs: [category],
        orderBy: 'expense_date DESC',
      );

      return List.generate(maps.length, (i) {
        return ExpenseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get expenses by category: $e');
    }
  }

  Future<List<ExpenseModel>> getExpensesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        where: 'expense_date BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
        orderBy: 'expense_date DESC',
      );

      return List.generate(maps.length, (i) {
        return ExpenseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get expenses by date range: $e');
    }
  }

  Future<double> getTotalExpensesByCategory(String category) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(amount) as total FROM expenses WHERE category = ?',
        [category],
      );

      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw Exception('Failed to get total expenses by category: $e');
    }
  }

  Future<double> getTotalExpensesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(amount) as total FROM expenses WHERE expense_date BETWEEN ? AND ?',
        [startDate.toIso8601String(), endDate.toIso8601String()],
      );

      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw Exception('Failed to get total expenses by date range: $e');
    }
  }
}
