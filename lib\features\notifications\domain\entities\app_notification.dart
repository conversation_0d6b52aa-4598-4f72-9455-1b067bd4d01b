import '../../data/models/app_notification_model.dart';

class AppNotification {
  final int? id;
  final String type;
  final String message;
  final DateTime date;
  final int? relatedEntityId;
  final String? relatedEntityType;
  final String? suggestedAction;
  final String? suggestedActionRoute;
  final bool isRead;
  final bool isActionTaken;

  const AppNotification({
    this.id,
    required this.type,
    required this.message,
    required this.date,
    this.relatedEntityId,
    this.relatedEntityType,
    this.suggestedAction,
    this.suggestedActionRoute,
    this.isRead = false,
    this.isActionTaken = false,
  });

  // Create AppNotification from AppNotificationModel
  factory AppNotification.fromModel(AppNotificationModel model) {
    return AppNotification(
      id: model.id,
      type: model.type,
      message: model.message,
      date: model.date,
      relatedEntityId: model.relatedEntityId,
      relatedEntityType: model.relatedEntityType,
      suggestedAction: model.suggestedAction,
      suggestedActionRoute: model.suggestedActionRoute,
      isRead: model.isRead,
      isActionTaken: model.isActionTaken,
    );
  }

  // Business logic methods
  bool get hasAction => suggestedAction != null && suggestedActionRoute != null;
  
  String get typeDisplayName {
    switch (type) {
      case 'low_stock_alert':
        return 'تنبيه مخزون منخفض';
      case 'price_change':
        return 'تغيير سعر';
      case 'order_reminder':
        return 'تذكير طلبية';
      case 'system_notification':
        return 'إشعار النظام';
      default:
        return type;
    }
  }

  String get statusText {
    if (isActionTaken) {
      return 'تم التعديل';
    } else if (isRead) {
      return 'مقروء';
    } else {
      return 'جديد';
    }
  }

  // Copy with method for updates
  AppNotification copyWith({
    int? id,
    String? type,
    String? message,
    DateTime? date,
    int? relatedEntityId,
    String? relatedEntityType,
    String? suggestedAction,
    String? suggestedActionRoute,
    bool? isRead,
    bool? isActionTaken,
  }) {
    return AppNotification(
      id: id ?? this.id,
      type: type ?? this.type,
      message: message ?? this.message,
      date: date ?? this.date,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      suggestedAction: suggestedAction ?? this.suggestedAction,
      suggestedActionRoute: suggestedActionRoute ?? this.suggestedActionRoute,
      isRead: isRead ?? this.isRead,
      isActionTaken: isActionTaken ?? this.isActionTaken,
    );
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, type: $type, message: $message, '
        'date: $date, relatedEntityId: $relatedEntityId, '
        'relatedEntityType: $relatedEntityType, suggestedAction: $suggestedAction, '
        'suggestedActionRoute: $suggestedActionRoute, isRead: $isRead, '
        'isActionTaken: $isActionTaken)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification &&
        other.id == id &&
        other.type == type &&
        other.message == message &&
        other.date == date &&
        other.relatedEntityId == relatedEntityId &&
        other.relatedEntityType == relatedEntityType &&
        other.suggestedAction == suggestedAction &&
        other.suggestedActionRoute == suggestedActionRoute &&
        other.isRead == isRead &&
        other.isActionTaken == isActionTaken;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        message.hashCode ^
        date.hashCode ^
        relatedEntityId.hashCode ^
        relatedEntityType.hashCode ^
        suggestedAction.hashCode ^
        suggestedActionRoute.hashCode ^
        isRead.hashCode ^
        isActionTaken.hashCode;
  }
}
