import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/reports/presentation/providers/analytics_provider.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AnalyticsProvider>().loadAllAnalytics();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: 'التقارير والتحليلات',
      customAppBar: AppBar(
        title: const Text('التقارير والتحليلات'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                context.read<AnalyticsProvider>().loadAllAnalytics(),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.date_range),
            onSelected: (period) =>
                context.read<AnalyticsProvider>().setQuickPeriod(period),
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'today', child: Text('اليوم')),
              const PopupMenuItem(value: 'week', child: Text('هذا الأسبوع')),
              const PopupMenuItem(value: 'month', child: Text('هذا الشهر')),
              const PopupMenuItem(value: 'year', child: Text('هذا العام')),
              const PopupMenuItem(value: 'all', child: Text('جميع الفترات')),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'نظرة عامة'),
            Tab(text: 'المبيعات'),
            Tab(text: 'المخزون'),
            Tab(text: 'الأرباح'),
          ],
        ),
      ),
      child: Consumer<AnalyticsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: TextStyle(fontSize: 16, color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadAllAnalytics(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(provider),
              _buildSalesTab(provider),
              _buildInventoryTab(provider),
              _buildProfitTab(provider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildOverviewTab(AnalyticsProvider provider) {
    return RefreshIndicator(
      onRefresh: () => provider.loadAllAnalytics(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفترة: ${provider.periodDescription}',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.0,
              children: [
                _buildKpiCard(
                  'إجمالي المبيعات',
                  provider.formattedRevenue,
                  Icons.trending_up,
                  Colors.green,
                ),
                _buildKpiCard(
                  'إجمالي التكلفة',
                  provider.formattedCost,
                  Icons.trending_down,
                  Colors.red,
                ),
                _buildKpiCard(
                  'الربح الإجمالي',
                  provider.formattedProfit,
                  Icons.account_balance_wallet,
                  Colors.blue,
                ),
                _buildKpiCard(
                  'قيمة المخزون',
                  provider.formattedInventoryValue,
                  Icons.inventory,
                  Colors.orange,
                ),
                _buildKpiCard(
                  'عدد المبيعات',
                  '${provider.totalSales}',
                  Icons.receipt,
                  Colors.purple,
                ),
                _buildKpiCard(
                  'المنتجات قليلة المخزون',
                  '${provider.lowStockCount}',
                  Icons.warning,
                  Colors.amber,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesTab(AnalyticsProvider provider) {
    return const Center(
      child: Text(
        'تبويب المبيعات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildInventoryTab(AnalyticsProvider provider) {
    return const Center(
      child: Text(
        'تبويب المخزون\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildProfitTab(AnalyticsProvider provider) {
    return const Center(
      child: Text(
        'تبويب الأرباح\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildKpiCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Flexible(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
