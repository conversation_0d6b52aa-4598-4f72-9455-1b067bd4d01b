import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';
import 'package:market/features/customers/domain/entities/customer.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/presentation/providers/category_provider.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/usecases/get_product_by_id.dart';
import 'package:market/features/sales/presentation/providers/sale_provider.dart';
import 'package:market/features/sales/domain/entities/sale.dart';
import 'package:market/features/sales/domain/entities/sale_item.dart';

// Helper class for UI display
class SaleItemDisplay {
  final int? productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final String itemType;
  final String? description;
  final String? unit; // إضافة الوحدة

  SaleItemDisplay({
    this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.itemType,
    this.description,
    this.unit,
  });

  double get totalPrice => quantity * unitPrice;
}

class SaleFormScreen extends StatefulWidget {
  final int? saleId; // إضافة معامل saleId للتعديل

  const SaleFormScreen({super.key, this.saleId});

  @override
  State<SaleFormScreen> createState() => _SaleFormScreenState();
}

class _SaleFormScreenState extends State<SaleFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _retailGoodsDescriptionController = TextEditingController();
  final _retailGoodsAmountController = TextEditingController();
  final _paidAmountController =
      TextEditingController(); // إضافة حقل المبلغ المدفوع
  final _paidAmountFocusNode = FocusNode(); // إضافة FocusNode للمبلغ المدفوع
  final _discountController = TextEditingController(); // حقل الخصم
  final _notesController = TextEditingController(); // حقل الملاحظات

  Customer? _selectedCustomer;
  String _paymentMethod = 'نقدي';
  final List<SaleItemDisplay> _saleItems = [];
  bool _isLoading = false;
  bool _isWalkInCustomer = true;

  // متغيرات للتعديل
  Sale? _existingSale;
  bool _isEditMode = false;
  bool _isLoadingExistingSale = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.saleId != null;

    // تهيئة حقل المبلغ المدفوع
    _paidAmountController.text = _isEditMode && _existingSale != null
        ? _existingSale!.totalPaidAmount.toString()
        : '0.0';

    // إضافة مستمع للتنظيف عند التركيز
    _paidAmountFocusNode.addListener(_onPaidAmountFocusChange);

    // إضافة مستمع للخصم لتحديث المبلغ المدفوع
    _discountController.addListener(_updatePaidAmountForCashPayment);

    if (_isEditMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadExistingSale();
      });
    }
  }

  void _onPaidAmountFocusChange() {
    if (_paidAmountFocusNode.hasFocus) {
      // عند التركيز على الحقل، إذا كانت القيمة 0 أو 0.0، امسحها
      final currentValue = _paidAmountController.text.trim();
      if (currentValue == '0' ||
          currentValue == '0.0' ||
          currentValue == '0.00') {
        _paidAmountController.clear();
      }
    }
  }

  @override
  void dispose() {
    _retailGoodsDescriptionController.dispose();
    _retailGoodsAmountController.dispose();
    _paidAmountController.dispose();
    _discountController.dispose();
    _notesController.dispose();
    _paidAmountFocusNode.removeListener(_onPaidAmountFocusChange);
    _paidAmountFocusNode.dispose();
    _discountController.removeListener(_updatePaidAmountForCashPayment);
    super.dispose();
  }

  // تحميل الفاتورة الموجودة للتعديل
  Future<void> _loadExistingSale() async {
    if (widget.saleId == null) return;

    setState(() => _isLoadingExistingSale = true);

    try {
      final saleProvider = context.read<SaleProvider>();
      final customerProvider = context.read<CustomerProvider>();

      // تحميل الفاتورة
      final sale = await saleProvider.getSaleById(widget.saleId!);
      if (sale == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      // تحميل عناصر الفاتورة
      final saleItems = await saleProvider.getSaleItems(widget.saleId!);

      // تحميل بيانات العميل إذا كان موجوداً
      Customer? customer;
      if (sale.customerId != null) {
        customer = await customerProvider.getCustomerById(sale.customerId!);
      }

      // تحويل SaleItems إلى SaleItemDisplay
      final List<SaleItemDisplay> displayItems = [];
      for (final item in saleItems) {
        String productName = item.description ?? 'بند غير محدد';

        if (item.isWholesaleProduct && item.productId != null) {
          // استخدام GetProductByIdUseCase مباشرة
          final getProductByIdUseCase = GetIt.instance<GetProductByIdUseCase>();
          final product = await getProductByIdUseCase.call(item.productId!);
          productName = product?.name ?? 'منتج رقم ${item.productId}';
        }

        displayItems.add(
          SaleItemDisplay(
            productId: item.productId,
            productName: productName,
            quantity: item.quantity ?? 1,
            unitPrice: item.unitPrice,
            itemType: item.itemType,
            description: item.description,
          ),
        );
      }

      if (mounted) {
        setState(() {
          _existingSale = sale;
          _selectedCustomer = customer;
          _isWalkInCustomer = sale.customerId == null;
          _paymentMethod = sale.paymentMethod;
          _paidAmountController.text = sale.totalPaidAmount.toString();
          _discountController.text = sale.discountAmount.toString();
          _notesController.text = sale.notes ?? '';
          _saleItems.clear();
          _saleItems.addAll(displayItems);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الفاتورة: $e')));
        Navigator.of(context).pop();
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingExistingSale = false);
      }
    }
  }

  double get _totalAmount {
    double total = 0;
    for (var item in _saleItems) {
      total += item.totalPrice;
    }
    // Add retail goods if any
    if (_retailGoodsAmountController.text.isNotEmpty) {
      total += double.tryParse(_retailGoodsAmountController.text) ?? 0;
    }
    return total;
  }

  double get _discountAmount {
    return double.tryParse(_discountController.text) ?? 0.0;
  }

  double get _netAmount {
    return _totalAmount - _discountAmount;
  }

  double get _dueAmount {
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;
    return _netAmount - paidAmount;
  }

  void _updatePaidAmountForCashPayment() {
    // تحديث المبلغ المدفوع تلقائياً للدفع النقدي عند تغيير الإجمالي
    if (_paymentMethod == 'نقدي' && _netAmount > 0) {
      _paidAmountController.text = _netAmount.toStringAsFixed(2);
    }
  }

  void _updatePaymentMethodLogic() {
    // تطبيق المنطق الديناميكي حسب طريقة الدفع
    switch (_paymentMethod) {
      case 'نقدي':
        // تعبئة المبلغ المدفوع بالصافي وتعطيل التعديل
        _paidAmountController.text = _netAmount.toStringAsFixed(2);
        break;
      case 'آجل':
        // تصفير المبلغ المدفوع وتعطيل التعديل
        _paidAmountController.text = '0.0';
        break;
      case 'مدفوع جزئياً':
        // السماح بالتعديل - لا نغير القيمة الحالية
        break;
    }
  }

  Future<void> _selectCustomer() async {
    try {
      final customers = await context
          .read<CustomerProvider>()
          .getAllCustomers();
      if (!mounted) return;

      final selected = await showDialog<Customer>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('اختيار العميل'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: customers.isEmpty
                ? const Center(child: Text('لا توجد عملاء'))
                : ListView.builder(
                    itemCount: customers.length,
                    itemBuilder: (context, index) {
                      final customer = customers[index];
                      return ListTile(
                        title: Text(customer.name),
                        subtitle: Text(customer.phone ?? 'لا يوجد هاتف'),
                        onTap: () => Navigator.of(context).pop(customer),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (selected != null) {
        setState(() {
          _selectedCustomer = selected;
          _isWalkInCustomer = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل العملاء: $e')));
      }
    }
  }

  void _addProduct() {
    showDialog(
      context: context,
      builder: (context) => _AddProductDialog(
        onItemAdded: (item) {
          setState(() {
            _saleItems.add(item);
            _updatePaidAmountForCashPayment();
          });
        },
      ),
    );
  }

  void _editItem(int index) {
    final item = _saleItems[index];
    showDialog(
      context: context,
      builder: (context) => _AddProductDialog(
        onItemAdded: (editedItem) {
          setState(() {
            _saleItems[index] = editedItem;
            _updatePaidAmountForCashPayment();
          });
        },
        initialItem: item,
      ),
    );
  }

  void _addRetailGoods() {
    if (_retailGoodsDescriptionController.text.trim().isEmpty ||
        _retailGoodsAmountController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال وصف ومبلغ البند')),
      );
      return;
    }

    final amount = double.tryParse(_retailGoodsAmountController.text.trim());
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال مبلغ صحيح')));
      return;
    }

    setState(() {
      _saleItems.add(
        SaleItemDisplay(
          productId: null,
          productName: _retailGoodsDescriptionController.text.trim(),
          quantity: 1,
          unitPrice: amount,
          itemType: 'retail_goods_summary',
          description: _retailGoodsDescriptionController.text.trim(),
        ),
      );
      _retailGoodsDescriptionController.clear();
      _retailGoodsAmountController.clear();
      _updatePaidAmountForCashPayment();
    });
  }

  void _removeItem(int index) {
    _confirmDelete(index);
  }

  void _confirmDelete(int index) {
    final item = _saleItems[index];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف "${item.productName}" من الفاتورة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _saleItems.removeAt(index);
                  _updatePaidAmountForCashPayment();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveSale() async {
    if (!_formKey.currentState!.validate()) return;

    if (_saleItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة عناصر للفاتورة')),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      final saleProvider = context.read<SaleProvider>();
      final paidAmount =
          double.tryParse(_paidAmountController.text.trim()) ?? 0.0;

      if (_isEditMode && _existingSale != null) {
        // تحديث الفاتورة الموجودة
        final updatedSale = Sale(
          id: _existingSale!.id,
          customerId: _isWalkInCustomer ? null : _selectedCustomer?.id,
          saleDate: _existingSale!.saleDate, // الحفاظ على تاريخ الإنشاء الأصلي
          totalAmount: _totalAmount,
          discountAmount: _discountAmount,
          totalPaidAmount: paidAmount, // استخدام المبلغ المدفوع من الحقل
          paymentMethod: _paymentMethod,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          status: _existingSale!.status,
          createdAt: _existingSale!.createdAt,
          updatedAt: DateTime.now(),
        );

        // تحديث الفاتورة مع عناصرها
        final saleItems = _saleItems
            .map(
              (item) => SaleItem(
                saleId: _existingSale!.id!,
                productId: item.productId!,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                itemType: item.itemType,
                description: item.description,
              ),
            )
            .toList();

        final success = await saleProvider.updateSaleWithItems(
          updatedSale,
          saleItems,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث فاتورة البيع بنجاح')),
          );
          context.pop();
        }
      } else {
        // Get paid amount from form first
        final paidAmount =
            double.tryParse(_paidAmountController.text.trim()) ?? 0.0;

        // إنشاء فاتورة جديدة
        final sale = Sale(
          customerId: _isWalkInCustomer ? null : _selectedCustomer?.id,
          saleDate: DateTime.now(),
          totalAmount: _totalAmount,
          discountAmount: _discountAmount,
          totalPaidAmount: 0.0, // سيتم حسابه في Provider
          paymentMethod: _paymentMethod,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          status: 'pending', // سيتم حسابه في Provider
        );

        // Create SaleItem entities
        final saleItems = _saleItems
            .map(
              (item) => SaleItem(
                saleId: 0, // Will be set by the database
                productId: item.productId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                itemType: item.itemType,
                description: item.description,
              ),
            )
            .toList();

        final success = await saleProvider.createSale(
          sale,
          saleItems,
          paidAmount: paidAmount,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء فاتورة البيع بنجاح')),
          );
          context.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الفاتورة: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض شاشة التحميل إذا كان يتم تحميل الفاتورة الموجودة
    if (_isLoadingExistingSale) {
      return const SecondaryScreenWrapper(
        title: 'تحميل الفاتورة...',
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return SecondaryScreenWrapper(
      title: _isEditMode ? 'تعديل فاتورة البيع' : 'إنشاء فاتورة بيع',
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // اختيار العميل
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'العميل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('عميل عابر'),
                              value: true,
                              groupValue: _isWalkInCustomer,
                              onChanged: (value) {
                                setState(() {
                                  _isWalkInCustomer = value!;
                                  _selectedCustomer = null;
                                });
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('عميل مسجل'),
                              value: false,
                              groupValue: _isWalkInCustomer,
                              onChanged: (value) {
                                setState(() {
                                  _isWalkInCustomer = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      if (!_isWalkInCustomer) ...[
                        const SizedBox(height: 8),
                        ListTile(
                          title: Text(_selectedCustomer?.name ?? 'اختر العميل'),
                          subtitle: Text(_selectedCustomer?.phone ?? ''),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _selectCustomer,
                          tileColor: Colors.grey.shade100,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // طريقة الدفع
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'طريقة الدفع',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  SegmentedButton<String>(
                    segments: const [
                      ButtonSegment<String>(
                        value: 'نقدي',
                        label: Text('نقدي'),
                        icon: Icon(Icons.money),
                      ),
                      ButtonSegment<String>(
                        value: 'آجل',
                        label: Text('آجل'),
                        icon: Icon(Icons.schedule),
                      ),
                      ButtonSegment<String>(
                        value: 'مدفوع جزئياً',
                        label: Text('جزئي'),
                        icon: Icon(Icons.payments),
                      ),
                    ],
                    selected: {_paymentMethod},
                    onSelectionChanged: (Set<String> newSelection) {
                      setState(() {
                        _paymentMethod = newSelection.first;
                        _updatePaymentMethodLogic();
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // إضافة منتج
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إضافة منتج',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: _addProduct,
                        icon: const Icon(Icons.add_shopping_cart),
                        label: const Text('اختيار منتج من المخزون'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // إضافة بند بقالة
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إضافة بند بقالة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _retailGoodsDescriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف البند',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _retailGoodsAmountController,
                              decoration: const InputDecoration(
                                labelText: 'المبلغ',
                                border: OutlineInputBorder(),
                                suffixText: 'ر.ي',
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9.]'),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _addRetailGoods,
                            child: const Text('إضافة'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // عناصر الفاتورة
              if (_saleItems.isNotEmpty) ...[
                const Text(
                  'عناصر الفاتورة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...List.generate(_saleItems.length, (index) {
                  final item = _saleItems[index];
                  return Card(
                    child: ListTile(
                      title: Text(
                        item.unit != null
                            ? '${item.productName} - ${item.unit}'
                            : item.productName,
                      ),
                      subtitle: Text(
                        'الكمية: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)} ر.ي',
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${item.totalPrice.toStringAsFixed(2)} ر.ي',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          IconButton(
                            icon: const Icon(Icons.edit, color: Colors.blue),
                            onPressed: () => _editItem(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _removeItem(index),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                const SizedBox(height: 16),
              ],

              // ملخص الفاتورة
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ملخص الفاتورة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // الإجمالي قبل الخصم
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('الإجمالي قبل الخصم:'),
                          Text(
                            '${_totalAmount.toStringAsFixed(2)} ر.ي',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // الخصم
                      if (_discountAmount > 0) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الخصم:'),
                            Text(
                              '- ${_discountAmount.toStringAsFixed(2)} ر.ي',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.red.shade600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                      ],

                      // الصافي بعد الخصم
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Colors.grey.shade300),
                            bottom: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'الصافي بعد الخصم:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${_netAmount.toStringAsFixed(2)} ر.ي',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),

                      // المبلغ المدفوع
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ المدفوع:'),
                          Text(
                            '${(double.tryParse(_paidAmountController.text) ?? 0.0).toStringAsFixed(2)} ر.ي',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // المبلغ المتبقي
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'المبلغ المتبقي:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${_dueAmount.toStringAsFixed(2)} ر.ي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: _dueAmount > 0
                                  ? Colors.red.shade600
                                  : Colors.green.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الخصم
              TextFormField(
                controller: _discountController,
                decoration: const InputDecoration(
                  labelText: 'مبلغ الخصم',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.discount),
                  suffixText: 'ر.ي',
                  helperText: 'اختياري - سيتم خصمه من الإجمالي',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*')),
                ],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final discount = double.tryParse(value);
                    if (discount == null || discount < 0) {
                      return 'مبلغ خصم غير صحيح';
                    }
                    if (discount >= _totalAmount) {
                      return 'الخصم لا يمكن أن يكون أكبر من أو يساوي الإجمالي';
                    }
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // إعادة بناء الواجهة لتحديث الحسابات
                  });
                },
              ),
              const SizedBox(height: 16),

              // حقل الملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                  helperText: 'اختياري - ملاحظات إضافية للفاتورة',
                ),
                maxLines: 3,
                textInputAction: TextInputAction.newline,
              ),
              const SizedBox(height: 16),

              // المبلغ المدفوع
              TextFormField(
                controller: _paidAmountController,
                focusNode: _paidAmountFocusNode,
                enabled:
                    _paymentMethod == 'مدفوع جزئياً', // تفعيل فقط للدفع الجزئي
                decoration: InputDecoration(
                  labelText: 'المبلغ المدفوع',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.payments),
                  suffixText: 'ر.ي',
                  helperText: _paymentMethod == 'نقدي'
                      ? 'يتم ملؤه تلقائياً للدفع النقدي'
                      : _paymentMethod == 'آجل'
                      ? 'صفر للدفع الآجل'
                      : 'يمكن تعديله للدفع الجزئي',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ المدفوع';
                  }
                  final paidAmount = double.tryParse(value);
                  if (paidAmount == null || paidAmount < 0) {
                    return 'مبلغ غير صحيح';
                  }
                  if (paidAmount > _netAmount) {
                    return 'المبلغ المدفوع أكبر من الصافي';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // إعادة بناء الواجهة لتحديث الإجمالي والمبلغ المستحق في الفاتورة
                  });
                },
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              ElevatedButton(
                onPressed: _isLoading ? null : _saveSale,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        _isEditMode ? 'تحديث الفاتورة' : 'حفظ الفاتورة',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'ملاحظة: وحدة المبيعات قيد التطوير. هذا النموذج للعرض فقط.',
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AddProductDialog extends StatefulWidget {
  final Function(SaleItemDisplay) onItemAdded;
  final SaleItemDisplay? initialItem;

  const _AddProductDialog({required this.onItemAdded, this.initialItem});

  @override
  State<_AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<_AddProductDialog> {
  final _quantityController = TextEditingController();
  final _searchController = TextEditingController();
  Product? _selectedProduct;
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  bool _isLoading = true;
  String? _selectedCategory;
  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    // If editing, set initial values
    if (widget.initialItem != null) {
      _quantityController.text = widget.initialItem!.quantity.toString();
    }

    // إضافة مستمعين للبحث والكمية
    _searchController.addListener(_filterProducts);
    _quantityController.addListener(() => setState(() {}));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProducts();
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    try {
      final productProvider = context.read<ProductProvider>();
      final categoryProvider = context.read<CategoryProvider>();

      await productProvider.fetchProducts();
      await categoryProvider.fetchCategories();

      if (mounted) {
        final products = productProvider.products;
        final categories = categoryProvider.categories;

        setState(() {
          _products = products;
          _filteredProducts = products; // تهيئة القائمة المفلترة
          _categories = ['الكل', ...categories.map((c) => c.name)];
          _isLoading = false;

          // If editing, find and select the initial product
          if (widget.initialItem != null &&
              widget.initialItem!.productId != null) {
            _selectedProduct = products.firstWhere(
              (p) => p.id == widget.initialItem!.productId,
              orElse: () => products.first,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المنتجات: $e')));
      }
    }
  }

  void _filterProducts() {
    final searchQuery = _searchController.text.toLowerCase();

    setState(() {
      _filteredProducts = _products.where((product) {
        final matchesSearch =
            searchQuery.isEmpty ||
            product.name.toLowerCase().contains(searchQuery) ||
            (product.barcode?.toLowerCase().contains(searchQuery) ?? false);

        final matchesCategory =
            _selectedCategory == null ||
            _selectedCategory == 'الكل' ||
            product.category == _selectedCategory;

        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _quantityController.text = '1'; // Default quantity
    });
  }

  bool _isQuantityValid() {
    if (_selectedProduct == null) return true;

    final quantity = double.tryParse(_quantityController.text.trim());
    if (quantity == null || quantity <= 0) return false;

    return quantity <= _selectedProduct!.totalQuantity;
  }

  void _addItem() {
    if (_selectedProduct == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار منتج')));
      return;
    }

    if (_quantityController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال الكمية')));
      return;
    }

    final quantity = double.tryParse(_quantityController.text.trim());

    if (quantity == null || quantity <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال كمية صحيحة')));
      return;
    }

    if (quantity > _selectedProduct!.totalQuantity) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'الكمية المطلوبة أكبر من المخزون المتاح (${_selectedProduct!.totalQuantity})',
          ),
        ),
      );
      return;
    }

    final item = SaleItemDisplay(
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      quantity: quantity.toInt(),
      unitPrice: _selectedProduct!.wholesalePrice,
      itemType: 'wholesale_product',
      unit: _selectedProduct!.unit,
    );

    widget.onItemAdded(item);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'تعديل المنتج' : 'إضافة منتج للفاتورة',
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 600, // زيادة الارتفاع لتجنب Overflow
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // البحث والفلترة
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: TextField(
                          controller: _searchController,
                          decoration: const InputDecoration(
                            labelText: 'البحث (اسم أو باركود)',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.search),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'الفئة',
                            border: OutlineInputBorder(),
                          ),
                          items: _categories.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value;
                              _filterProducts();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // اختيار المنتج
                  Expanded(
                    child: ListView.builder(
                      itemCount: _filteredProducts.length,
                      itemBuilder: (context, index) {
                        final product = _filteredProducts[index];
                        final isSelected = _selectedProduct?.id == product.id;
                        return Card(
                          color: isSelected ? Colors.blue.shade50 : null,
                          child: ListTile(
                            title: Text(product.name),
                            subtitle: Text(
                              'سعر الجملة: ${product.wholesalePrice.toStringAsFixed(2)} ر.ي\n'
                              'المخزون: ${product.totalQuantity} | الفئة: ${product.category}',
                            ),
                            trailing: isSelected
                                ? const Icon(
                                    Icons.check_circle,
                                    color: Colors.blue,
                                  )
                                : null,
                            onTap: () => _selectProduct(product),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  // الكمية
                  if (_selectedProduct != null) ...[
                    TextField(
                      controller: _quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية',
                        border: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: _isQuantityValid()
                                ? Colors.grey
                                : Colors.red,
                            width: _isQuantityValid() ? 1.0 : 2.0,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: _isQuantityValid()
                                ? Colors.grey
                                : Colors.red,
                            width: _isQuantityValid() ? 1.0 : 2.0,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: _isQuantityValid()
                                ? Colors.blue
                                : Colors.red,
                            width: 2.0,
                          ),
                        ),
                        helperText:
                            'المتاح: ${_selectedProduct!.totalQuantity}',
                        errorText: _isQuantityValid()
                            ? null
                            : 'الكمية أكبر من المتاح',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade200),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'الإجمالي: ${(_selectedProduct!.wholesalePrice * (double.tryParse(_quantityController.text) ?? 0)).toStringAsFixed(2)} ر.ي',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        if (_selectedProduct != null)
          ElevatedButton(
            onPressed: _isQuantityValid() ? _addItem : null,
            child: Text(widget.initialItem != null ? 'تحديث' : 'إضافة'),
          ),
      ],
    );
  }
}
