import '../../domain/entities/app_notification.dart';
import '../../domain/repositories/notification_repository.dart';
import '../datasources/notification_database_service.dart';
import '../models/app_notification_model.dart';

class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationDatabaseService _databaseService;

  NotificationRepositoryImpl(this._databaseService);

  @override
  Future<int> createNotification(AppNotification notification) async {
    try {
      final notificationModel = AppNotificationModel(
        id: notification.id,
        type: notification.type,
        message: notification.message,
        date: notification.date,
        relatedEntityId: notification.relatedEntityId,
        relatedEntityType: notification.relatedEntityType,
        suggestedAction: notification.suggestedAction,
        suggestedActionRoute: notification.suggestedActionRoute,
        isRead: notification.isRead,
        isActionTaken: notification.isActionTaken,
      );
      
      return await _databaseService.createNotification(notificationModel);
    } catch (e) {
      throw Exception('Failed to create notification: $e');
    }
  }

  @override
  Future<List<AppNotification>> getNotifications({int limit = 50, int offset = 0}) async {
    try {
      final notificationModels = await _databaseService.getNotifications(
        limit: limit,
        offset: offset,
      );
      return notificationModels.map((model) => AppNotification.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get notifications: $e');
    }
  }

  @override
  Future<int> getUnreadNotificationsCount() async {
    try {
      return await _databaseService.getUnreadNotificationsCount();
    } catch (e) {
      throw Exception('Failed to get unread notifications count: $e');
    }
  }

  @override
  Future<void> markNotificationAsRead(int id) async {
    try {
      await _databaseService.markNotificationAsRead(id);
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  @override
  Future<void> markNotificationActionTaken(int id) async {
    try {
      await _databaseService.markNotificationActionTaken(id);
    } catch (e) {
      throw Exception('Failed to mark notification action as taken: $e');
    }
  }

  @override
  Future<List<AppNotification>> getNotificationsByType(String type) async {
    try {
      final notificationModels = await _databaseService.getNotificationsByType(type);
      return notificationModels.map((model) => AppNotification.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get notifications by type: $e');
    }
  }

  @override
  Future<List<AppNotification>> getNotificationsForEntity(
    String entityType,
    int entityId,
  ) async {
    try {
      final notificationModels = await _databaseService.getNotificationsForEntity(
        entityType,
        entityId,
      );
      return notificationModels.map((model) => AppNotification.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get notifications for entity: $e');
    }
  }

  @override
  Future<void> deleteNotification(int id) async {
    try {
      await _databaseService.deleteNotification(id);
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  @override
  Future<void> deleteOldNotifications(int daysOld) async {
    try {
      await _databaseService.deleteOldNotifications(daysOld);
    } catch (e) {
      throw Exception('Failed to delete old notifications: $e');
    }
  }

  @override
  Future<void> markAllNotificationsAsRead() async {
    try {
      await _databaseService.markAllNotificationsAsRead();
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getNotificationStatistics() async {
    try {
      return await _databaseService.getNotificationStatistics();
    } catch (e) {
      throw Exception('Failed to get notification statistics: $e');
    }
  }
}
