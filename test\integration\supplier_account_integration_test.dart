import 'package:flutter_test/flutter_test.dart';
import 'package:market/features/suppliers/domain/entities/supplier_account.dart';
import 'package:market/features/suppliers/domain/usecases/add_supplier_account_entry.dart';
import 'package:market/features/suppliers/domain/usecases/get_supplier_account_statement.dart';
import 'package:market/features/suppliers/data/repositories/supplier_account_repository_impl.dart';
import 'package:market/features/suppliers/data/datasources/supplier_account_database_service.dart';
import 'package:market/core/database/database_service.dart';

void main() {
  group('Supplier Account Integration Tests', () {
    late DatabaseService databaseService;
    late SupplierAccountDatabaseService dataSource;
    late SupplierAccountRepositoryImpl repository;
    late AddSupplierAccountEntryUseCase addEntryUseCase;
    late GetSupplierAccountStatementUseCase getStatementUseCase;

    setUpAll(() async {
      // Initialize database service
      databaseService = DatabaseService.instance;
      await databaseService.database;

      // Initialize components
      dataSource = SupplierAccountDatabaseService(databaseService);
      repository = SupplierAccountRepositoryImpl(dataSource);
      addEntryUseCase = AddSupplierAccountEntryUseCase(repository);
      getStatementUseCase = GetSupplierAccountStatementUseCase(repository);
    });

    tearDown(() async {
      // Clean up test data
      final db = await databaseService.database;
      await db.delete('supplier_accounts');
    });

    test('should add supplier account entry and retrieve it', () async {
      // Arrange
      final entry = SupplierAccount(
        supplierId: 1,
        transactionDate: DateTime.now(),
        type: 'purchase_invoice',
        amount: 1000.0,
        description: 'Test purchase invoice',
        relatedInvoiceId: 1,
      );

      // Act
      final entryId = await addEntryUseCase.call(entry);
      final statement = await getStatementUseCase.call(1);

      // Assert
      expect(entryId, greaterThan(0));
      expect(statement, isNotEmpty);
      expect(statement.first.supplierId, 1);
      expect(statement.first.type, 'purchase_invoice');
      expect(statement.first.amount, 1000.0);
      expect(statement.first.isDebit, true);
      expect(statement.first.isCredit, false);
    });

    test('should handle payment_out transactions correctly', () async {
      // Arrange
      final purchaseEntry = SupplierAccount(
        supplierId: 2,
        transactionDate: DateTime.now().subtract(const Duration(days: 1)),
        type: 'purchase_invoice',
        amount: 1500.0,
        description: 'Purchase invoice',
        relatedInvoiceId: 2,
      );

      final paymentEntry = SupplierAccount(
        supplierId: 2,
        transactionDate: DateTime.now(),
        type: 'payment_out',
        amount: 500.0,
        description: 'Payment to supplier',
      );

      // Act
      await addEntryUseCase.call(purchaseEntry);
      await addEntryUseCase.call(paymentEntry);
      final statement = await getStatementUseCase.call(2);

      // Assert
      expect(statement, hasLength(2));
      
      // Check purchase entry
      final purchase = statement.firstWhere((entry) => entry.type == 'purchase_invoice');
      expect(purchase.isDebit, true);
      expect(purchase.isCredit, false);
      expect(purchase.amount, 1500.0);

      // Check payment entry
      final payment = statement.firstWhere((entry) => entry.type == 'payment_out');
      expect(payment.isDebit, false);
      expect(payment.isCredit, true);
      expect(payment.amount, 500.0);
    });

    test('should validate entry data correctly', () async {
      // Test invalid supplier ID
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: 0,
          transactionDate: DateTime.now(),
          type: 'purchase_invoice',
          amount: 100.0,
        )),
        throwsException,
      );

      // Test invalid amount
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: 1,
          transactionDate: DateTime.now(),
          type: 'purchase_invoice',
          amount: 0.0,
        )),
        throwsException,
      );

      // Test invalid transaction type
      expect(
        () => addEntryUseCase.call(SupplierAccount(
          supplierId: 1,
          transactionDate: DateTime.now(),
          type: 'invalid_type',
          amount: 100.0,
        )),
        throwsException,
      );
    });

    test('should filter statement by date range', () async {
      // Arrange
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(days: 1));
      final tomorrow = now.add(const Duration(days: 1));

      final entry1 = SupplierAccount(
        supplierId: 3,
        transactionDate: yesterday,
        type: 'purchase_invoice',
        amount: 100.0,
        description: 'Yesterday entry',
      );

      final entry2 = SupplierAccount(
        supplierId: 3,
        transactionDate: now,
        type: 'purchase_invoice',
        amount: 200.0,
        description: 'Today entry',
      );

      final entry3 = SupplierAccount(
        supplierId: 3,
        transactionDate: tomorrow,
        type: 'purchase_invoice',
        amount: 300.0,
        description: 'Tomorrow entry',
      );

      // Act
      await addEntryUseCase.call(entry1);
      await addEntryUseCase.call(entry2);
      await addEntryUseCase.call(entry3);

      // Test filtering by date range
      final todayStatement = await repository.getSupplierAccountStatement(
        3,
        fromDate: DateTime(now.year, now.month, now.day),
        toDate: DateTime(now.year, now.month, now.day, 23, 59, 59),
      );

      // Assert
      expect(todayStatement, hasLength(1));
      expect(todayStatement.first.description, 'Today entry');
      expect(todayStatement.first.amount, 200.0);
    });
  });
}
