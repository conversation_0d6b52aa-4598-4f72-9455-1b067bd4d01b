import '../../domain/entities/supplier_account.dart';

class SupplierAccountModel {
  final int? id;
  final int supplierId;
  final DateTime transactionDate;
  final String type;
  final double amount;
  final String? description;
  final int? relatedInvoiceId;

  const SupplierAccountModel({
    this.id,
    required this.supplierId,
    required this.transactionDate,
    required this.type,
    required this.amount,
    this.description,
    this.relatedInvoiceId,
  });

  /// Convert to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'supplierId': supplierId,
      'transactionDate': transactionDate.toIso8601String(),
      'type': type,
      'amount': amount,
      'description': description,
      'relatedInvoiceId': relatedInvoiceId,
    };
  }

  /// Create from Map (database result)
  factory SupplierAccountModel.fromMap(Map<String, dynamic> map) {
    return SupplierAccountModel(
      id: map['id']?.toInt(),
      supplierId: map['supplierId']?.toInt() ?? 0,
      transactionDate: DateTime.parse(map['transactionDate'] ?? DateTime.now().toIso8601String()),
      type: map['type'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      description: map['description'],
      relatedInvoiceId: map['relatedInvoiceId']?.toInt(),
    );
  }

  /// Create from Entity
  factory SupplierAccountModel.fromEntity(SupplierAccount entity) {
    return SupplierAccountModel(
      id: entity.id,
      supplierId: entity.supplierId,
      transactionDate: entity.transactionDate,
      type: entity.type,
      amount: entity.amount,
      description: entity.description,
      relatedInvoiceId: entity.relatedInvoiceId,
    );
  }

  /// Convert to Entity
  SupplierAccount toEntity() {
    return SupplierAccount(
      id: id,
      supplierId: supplierId,
      transactionDate: transactionDate,
      type: type,
      amount: amount,
      description: description,
      relatedInvoiceId: relatedInvoiceId,
    );
  }

  /// Copy with method for updates
  SupplierAccountModel copyWith({
    int? id,
    int? supplierId,
    DateTime? transactionDate,
    String? type,
    double? amount,
    String? description,
    int? relatedInvoiceId,
  }) {
    return SupplierAccountModel(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      transactionDate: transactionDate ?? this.transactionDate,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SupplierAccountModel &&
        other.id == id &&
        other.supplierId == supplierId &&
        other.transactionDate == transactionDate &&
        other.type == type &&
        other.amount == amount &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      supplierId,
      transactionDate,
      type,
      amount,
      description,
      relatedInvoiceId,
    );
  }

  @override
  String toString() {
    return 'SupplierAccountModel(id: $id, supplierId: $supplierId, transactionDate: $transactionDate, type: $type, amount: $amount, description: $description, relatedInvoiceId: $relatedInvoiceId)';
  }
}
