class PurchaseItemModel {
  final int? id;
  final int purchaseId;
  final int productId;
  final int quantity;
  final double unitPrice;

  const PurchaseItemModel({
    this.id,
    required this.purchaseId,
    required this.productId,
    required this.quantity,
    required this.unitPrice,
  });

  // Convert from Map (from database)
  factory PurchaseItemModel.fromMap(Map<String, dynamic> map) {
    return PurchaseItemModel(
      id: map['id'] as int?,
      purchaseId: map['purchaseId'] as int,
      productId: map['productId'] as int,
      quantity: map['quantity'] as int,
      unitPrice: (map['unitPrice'] as num).toDouble(),
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'purchaseId': purchaseId,
      'productId': productId,
      'quantity': quantity,
      'unitPrice': unitPrice,
    };
  }

  // Business logic methods
  double get totalPrice => quantity * unitPrice;

  // Copy with method for updates
  PurchaseItemModel copyWith({
    int? id,
    int? purchaseId,
    int? productId,
    int? quantity,
    double? unitPrice,
  }) {
    return PurchaseItemModel(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
    );
  }

  @override
  String toString() {
    return 'PurchaseItemModel(id: $id, purchaseId: $purchaseId, productId: $productId, '
        'quantity: $quantity, unitPrice: $unitPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseItemModel &&
        other.id == id &&
        other.purchaseId == purchaseId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        purchaseId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode;
  }
}
