import '../repositories/analytics_repository.dart';

class GetProfitSummaryUseCase {
  final AnalyticsRepository _repository;

  GetProfitSummaryUseCase(this._repository);

  Future<Map<String, dynamic>> call({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    // Validation
    if (fromDate != null && toDate != null && fromDate.isAfter(toDate)) {
      throw Exception('From date cannot be after to date');
    }

    try {
      return await _repository.getProfitAnalysis(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get profit summary: $e');
    }
  }
}
