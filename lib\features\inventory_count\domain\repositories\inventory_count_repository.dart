import '../entities/inventory_count.dart';

abstract class InventoryCountRepository {
  /// Create a new inventory count
  Future<int> createInventoryCount(InventoryCount inventoryCount);

  /// Get all inventory counts
  Future<List<InventoryCount>> getAllInventoryCounts();

  /// Get inventory count by ID
  Future<InventoryCount?> getInventoryCountById(int id);

  /// Get inventory counts by product
  Future<List<InventoryCount>> getInventoryCountsByProduct(int productId);

  /// Get inventory counts by date range
  Future<List<InventoryCount>> getInventoryCountsByDateRange(
    DateTime fromDate,
    DateTime toDate,
  );

  /// Update inventory count
  Future<void> updateInventoryCount(InventoryCount inventoryCount);

  /// Delete inventory count
  Future<void> deleteInventoryCount(int id);

  /// Get inventory count statistics
  Future<Map<String, dynamic>> getInventoryCountStatistics();

  /// Get products that need counting
  Future<List<Map<String, dynamic>>> getProductsNeedingCount({int daysSinceLastCount = 30});
}
