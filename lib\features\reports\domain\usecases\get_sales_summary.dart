import '../repositories/analytics_repository.dart';

class GetSalesSummaryUseCase {
  final AnalyticsRepository _repository;

  GetSalesSummaryUseCase(this._repository);

  Future<Map<String, dynamic>> call({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    // Validation
    if (fromDate != null && toDate != null && fromDate.isAfter(toDate)) {
      throw Exception('From date cannot be after to date');
    }

    try {
      return await _repository.getSalesSummary(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get sales summary: $e');
    }
  }
}
