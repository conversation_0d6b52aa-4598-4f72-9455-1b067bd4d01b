import '../../domain/entities/payment_receipt.dart';
import '../../domain/repositories/payment_receipt_repository.dart';
import '../datasources/payment_receipt_database_service.dart';
import '../models/payment_receipt_model.dart';

class PaymentReceiptRepositoryImpl implements PaymentReceiptRepository {
  final PaymentReceiptDatabaseService _databaseService;

  PaymentReceiptRepositoryImpl(this._databaseService);

  @override
  Future<int> createPaymentReceipt(PaymentReceipt paymentReceipt) async {
    try {
      final model = PaymentReceiptModel.fromEntity(paymentReceipt);
      return await _databaseService.createPaymentReceipt(model);
    } catch (e) {
      throw Exception('Failed to create payment receipt: $e');
    }
  }

  @override
  Future<PaymentReceipt?> getPaymentReceiptById(int id) async {
    try {
      final model = await _databaseService.getPaymentReceiptById(id);
      return model?.toEntity();
    } catch (e) {
      throw Exception('Failed to get payment receipt by id: $e');
    }
  }

  @override
  Future<List<PaymentReceipt>> getPaymentReceiptsByEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      final models = await _databaseService.getPaymentReceiptsByEntity(
        entityId: entityId,
        entityType: entityType,
      );
      return models.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get payment receipts by entity: $e');
    }
  }

  @override
  Future<List<PaymentReceipt>> getPaymentReceiptsByInvoice(int invoiceId) async {
    try {
      final models = await _databaseService.getPaymentReceiptsByInvoice(invoiceId);
      return models.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get payment receipts by invoice: $e');
    }
  }

  @override
  Future<List<PaymentReceipt>> getAllPaymentReceipts() async {
    try {
      final models = await _databaseService.getAllPaymentReceipts();
      return models.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get all payment receipts: $e');
    }
  }

  @override
  Future<void> updatePaymentReceipt(PaymentReceipt paymentReceipt) async {
    try {
      final model = PaymentReceiptModel.fromEntity(paymentReceipt);
      await _databaseService.updatePaymentReceipt(model);
    } catch (e) {
      throw Exception('Failed to update payment receipt: $e');
    }
  }

  @override
  Future<void> deletePaymentReceipt(int id) async {
    try {
      await _databaseService.deletePaymentReceipt(id);
    } catch (e) {
      throw Exception('Failed to delete payment receipt: $e');
    }
  }

  @override
  Future<List<PaymentReceipt>> getPaymentReceiptsByDateRange({
    required DateTime fromDate,
    required DateTime toDate,
    String? entityType,
    int? entityId,
  }) async {
    try {
      final models = await _databaseService.getPaymentReceiptsByDateRange(
        fromDate: fromDate,
        toDate: toDate,
        entityType: entityType,
        entityId: entityId,
      );
      return models.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get payment receipts by date range: $e');
    }
  }

  @override
  Future<double> getTotalPaymentsForEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      return await _databaseService.getTotalPaymentsForEntity(
        entityId: entityId,
        entityType: entityType,
      );
    } catch (e) {
      throw Exception('Failed to get total payments for entity: $e');
    }
  }

  @override
  Future<double> getTotalPaymentsForInvoice(int invoiceId) async {
    try {
      return await _databaseService.getTotalPaymentsForInvoice(invoiceId);
    } catch (e) {
      throw Exception('Failed to get total payments for invoice: $e');
    }
  }
}
