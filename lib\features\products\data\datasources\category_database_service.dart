import 'package:sqflite/sqflite.dart';
import 'package:market/core/database/database_service.dart';
import 'package:market/features/products/data/models/category_model.dart';

class CategoryDatabaseService {
  static const String tableName = 'categories';

  Future<Database> get _database async {
    return await DatabaseService.instance.database;
  }

  // Create a new category
  Future<int> createCategory(CategoryModel category) async {
    try {
      final db = await _database;
      final categoryMap = category.toMap();
      categoryMap.remove('id'); // Remove id for auto-increment
      
      final id = await db.insert(
        tableName,
        categoryMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      return id;
    } catch (e) {
      throw Exception('Failed to create category: $e');
    }
  }

  // Get all categories
  Future<List<CategoryModel>> getCategories() async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return CategoryModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  // Get category by ID
  Future<CategoryModel?> getCategoryById(int id) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return CategoryModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get category by id: $e');
    }
  }

  // Update category
  Future<int> updateCategory(CategoryModel category) async {
    try {
      final db = await _database;
      final result = await db.update(
        tableName,
        category.toMap(),
        where: 'id = ?',
        whereArgs: [category.id],
      );
      
      return result;
    } catch (e) {
      throw Exception('Failed to update category: $e');
    }
  }

  // Delete category
  Future<int> deleteCategory(int id) async {
    try {
      final db = await _database;
      final result = await db.delete(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      return result;
    } catch (e) {
      throw Exception('Failed to delete category: $e');
    }
  }

  // Search categories by name
  Future<List<CategoryModel>> searchCategories(String query) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'name LIKE ?',
        whereArgs: ['%$query%'],
        orderBy: 'name ASC',
      );

      return List.generate(maps.length, (i) {
        return CategoryModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to search categories: $e');
    }
  }

  // Check if category exists by name
  Future<bool> categoryExists(String name) async {
    try {
      final db = await _database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'LOWER(name) = LOWER(?)',
        whereArgs: [name],
        limit: 1,
      );

      return maps.isNotEmpty;
    } catch (e) {
      throw Exception('Failed to check if category exists: $e');
    }
  }
}
