import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/activity.dart';
import '../../domain/usecases/get_recent_activities.dart';

class ActivityProvider extends ChangeNotifier {
  final GetRecentActivitiesUseCase _getRecentActivitiesUseCase;

  ActivityProvider()
      : _getRecentActivitiesUseCase = GetIt.instance<GetRecentActivitiesUseCase>();

  // State variables
  final List<Activity> _activities = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMore = true;
  String? _errorMessage;
  Map<String, dynamic> _statistics = {};

  // Pagination
  static const int _pageSize = 10;
  int _currentOffset = 0;

  // Getters
  List<Activity> get activities => _activities;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasMore => _hasMore;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic> get statistics => _statistics;

  // Fetch initial activities
  Future<void> fetchActivities() async {
    _setLoading(true);
    _clearError();
    _currentOffset = 0;
    _hasMore = true;

    try {
      _activities.clear();
      final newActivities = await _getRecentActivitiesUseCase.call(
        limit: _pageSize,
        offset: _currentOffset,
      );
      
      _activities.addAll(newActivities);
      _currentOffset += _pageSize;
      
      // Check if we have more data
      if (newActivities.length < _pageSize) {
        _hasMore = false;
      }
      
      // Fetch statistics
      await _fetchStatistics();
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load activities: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load more activities (for infinite scrolling)
  Future<void> loadMoreActivities() async {
    if (_isLoadingMore || !_hasMore) return;

    _setLoadingMore(true);
    _clearError();

    try {
      final newActivities = await _getRecentActivitiesUseCase.call(
        limit: _pageSize,
        offset: _currentOffset,
      );
      
      _activities.addAll(newActivities);
      _currentOffset += _pageSize;
      
      // Check if we have more data
      if (newActivities.length < _pageSize) {
        _hasMore = false;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load more activities: ${e.toString()}');
    } finally {
      _setLoadingMore(false);
    }
  }

  // Fetch recent activities only (last 7 days)
  Future<void> fetchRecentActivities() async {
    _setLoading(true);
    _clearError();

    try {
      _activities.clear();
      final recentActivities = await _getRecentActivitiesUseCase.call(
        limit: 20,
        recentOnly: true,
      );
      
      _activities.addAll(recentActivities);
      _hasMore = false; // Recent activities don't support pagination
      
      await _fetchStatistics();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load recent activities: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Fetch statistics
  Future<void> _fetchStatistics() async {
    try {
      _statistics = await _getRecentActivitiesUseCase.getStatistics();
    } catch (e) {
      // Don't show error for statistics, just log it
      debugPrint('Failed to fetch statistics: $e');
    }
  }

  // Get activities by type
  List<Activity> getActivitiesByType(String type) {
    return _activities.where((activity) => activity.type == type).toList();
  }

  // Get activities count by type
  int getActivitiesCountByType(String type) {
    return _activities.where((activity) => activity.type == type).length;
  }

  // Get today's activities
  List<Activity> getTodayActivities() {
    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);
    final todayEnd = todayStart.add(const Duration(days: 1));
    
    return _activities.where((activity) {
      return activity.date.isAfter(todayStart) && activity.date.isBefore(todayEnd);
    }).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setLoadingMore(bool loading) {
    _isLoadingMore = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear activities list
  void clearActivities() {
    _activities.clear();
    _currentOffset = 0;
    _hasMore = true;
    _statistics.clear();
    notifyListeners();
  }

  // Refresh activities
  Future<void> refreshActivities() async {
    await fetchActivities();
  }

  // Initialize - fetch activities
  Future<void> initialize() async {
    await fetchActivities();
  }
}
