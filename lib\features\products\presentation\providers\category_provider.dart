import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:market/features/products/domain/entities/category.dart';
import 'package:market/features/products/domain/usecases/get_all_categories.dart';
import 'package:market/features/products/domain/usecases/create_category.dart';
import 'package:market/features/products/domain/usecases/update_category.dart';
import 'package:market/features/products/domain/usecases/delete_category.dart';

class CategoryProvider extends ChangeNotifier {
  final GetAllCategoriesUseCase _getAllCategoriesUseCase;
  final CreateCategoryUseCase _createCategoryUseCase;
  final UpdateCategoryUseCase _updateCategoryUseCase;
  final DeleteCategoryUseCase _deleteCategoryUseCase;

  CategoryProvider()
      : _getAllCategoriesUseCase = GetIt.instance<GetAllCategoriesUseCase>(),
        _createCategoryUseCase = GetIt.instance<CreateCategoryUseCase>(),
        _updateCategoryUseCase = GetIt.instance<UpdateCategoryUseCase>(),
        _deleteCategoryUseCase = GetIt.instance<DeleteCategoryUseCase>();

  // State variables
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _errorMessage;
  Category? _selectedCategory;

  // Getters
  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Category? get selectedCategory => _selectedCategory;

  // Fetch all categories
  Future<void> fetchCategories() async {
    _setLoading(true);
    _clearError();

    try {
      _categories = await _getAllCategoriesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load categories: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Add new category
  Future<bool> addCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      await _createCategoryUseCase.call(category);
      await fetchCategories(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to add category: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update existing category
  Future<bool> updateCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateCategoryUseCase.call(category);
      await fetchCategories(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to update category: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete category
  Future<bool> deleteCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteCategoryUseCase.call(categoryId);
      await fetchCategories(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to delete category: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Set selected category
  void setSelectedCategory(Category? category) {
    _selectedCategory = category;
    notifyListeners();
  }

  // Clear selected category
  void clearSelectedCategory() {
    _selectedCategory = null;
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Search categories
  List<Category> searchCategories(String query) {
    if (query.isEmpty) return _categories;
    
    return _categories.where((category) {
      return category.name.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Get category names for dropdown
  List<String> getCategoryNames() {
    return _categories.map((category) => category.name).toList();
  }

  // Find category by name
  Category? findCategoryByName(String name) {
    try {
      return _categories.firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }
}
