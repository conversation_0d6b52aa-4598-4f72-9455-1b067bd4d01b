import 'package:market/features/products/domain/entities/category.dart';
import 'package:market/features/products/domain/repositories/category_repository.dart';

class UpdateCategoryUseCase {
  final CategoryRepository _repository;

  UpdateCategoryUseCase(this._repository);

  Future<void> call(Category category) async {
    // Validate category data
    if (category.id == null) {
      throw Exception('Category ID cannot be null for update');
    }
    
    if (category.name.trim().isEmpty) {
      throw Exception('Category name cannot be empty');
    }

    await _repository.updateCategory(category);
  }
}
