import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';
import 'package:market/features/purchases/domain/entities/purchase.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';
import 'package:market/features/purchases/presentation/providers/purchase_provider.dart';
import 'package:market/features/transactions/presentation/providers/payment_receipt_provider.dart';

class SupplierAccountStatementDetailsScreen extends StatefulWidget {
  final String supplierId;

  const SupplierAccountStatementDetailsScreen({
    super.key,
    required this.supplierId,
  });

  @override
  State<SupplierAccountStatementDetailsScreen> createState() =>
      _SupplierAccountStatementDetailsScreenState();
}

class _SupplierAccountStatementDetailsScreenState
    extends State<SupplierAccountStatementDetailsScreen> {
  Supplier? _supplier;
  List<dynamic> _transactions = []; // قائمة موحدة للمشتريات والسندات
  double _totalPurchases = 0.0;
  double _totalPaymentsMade = 0.0;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSupplierStatement();
  }

  Future<void> _loadSupplierStatement() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final supplierProvider = context.read<SupplierProvider>();
      final purchaseProvider = context.read<PurchaseProvider>();
      final paymentReceiptProvider = context.read<PaymentReceiptProvider>();
      final int supplierIdInt = int.parse(widget.supplierId);

      _supplier = await supplierProvider.getSupplierById(supplierIdInt);

      // جلب فواتير الشراء
      final purchases = await purchaseProvider.getPurchasesBySupplier(
        supplierIdInt,
      );

      // جلب السندات المرتبطة بالمورد
      final paymentReceipts = await paymentReceiptProvider
          .getPaymentReceiptsByEntity(
            entityId: supplierIdInt,
            entityType: 'supplier',
          );

      // دمج القوائم وترتيبها حسب التاريخ
      _transactions = [...purchases, ...paymentReceipts];
      _transactions.sort((a, b) {
        final dateA = a is Purchase ? a.purchaseDate : a.transactionDate;
        final dateB = b is Purchase ? b.purchaseDate : b.transactionDate;
        return dateB.compareTo(dateA);
      });

      final summary = await supplierProvider.getSupplierFinancialSummary(
        supplierIdInt,
      );
      _totalPurchases = summary['totalPurchases'] ?? 0.0;
      _totalPaymentsMade = summary['totalPayments'] ?? 0.0;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'فشل في تحميل كشف حساب المورد: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _supplier?.name ?? 'كشف حساب المورد',
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // الانتقال إلى شاشة إضافة سند مع تمرير معلومات المورد
          final result = await context.push(
            '/transactions/receipts/new',
            extra: {
              'relatedEntityType': 'supplier',
              'relatedEntityId': int.parse(widget.supplierId),
            },
          );

          // إعادة تحميل البيانات إذا تم إنشاء سند بنجاح
          if (result == true) {
            await _loadSupplierStatement();
          }
        },
        tooltip: 'إضافة سند دفع',
        child: const Icon(Icons.add),
      ),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadSupplierStatement,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            )
          : Column(
              children: [
                _buildSupplierHeader(),
                const Divider(),
                Expanded(child: _buildTransactionsList()),
              ],
            ),
    );
  }

  Widget _buildSupplierHeader() {
    final currentBalance = _totalPurchases - _totalPaymentsMade;
    final balanceColor = currentBalance > 0
        ? Colors.red
        : (currentBalance < 0 ? Colors.green : Colors.grey);
    final balanceText = currentBalance > 0
        ? 'مدين بـ ${currentBalance.toStringAsFixed(2)} ر.ي'
        : (currentBalance < 0
              ? 'دائن بـ ${(-currentBalance).toStringAsFixed(2)} ر.ي'
              : 'رصيد صفر');

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 28,
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  child: Text(
                    _supplier?.name.isNotEmpty == true
                        ? _supplier!.name[0].toUpperCase()
                        : 'م',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _supplier?.name ?? 'مورد غير معروف',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            Text(
              'الرصيد الحالي:',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Text(
              balanceText,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: balanceColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'إجمالي المشتريات: ${_totalPurchases.toStringAsFixed(2)} ر.ي',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 4),
            Text(
              'إجمالي المدفوعات: ${_totalPaymentsMade.toStringAsFixed(2)} ر.ي',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList() {
    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد معاملات لهذا المورد',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];

        if (transaction is Purchase) {
          // عرض فاتورة شراء
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            elevation: 1,
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.orange.withValues(alpha: 0.1),
                child: const Icon(Icons.shopping_cart, color: Colors.orange),
              ),
              title: Text(
                'فاتورة شراء رقم ${transaction.id}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                '${transaction.notes ?? 'لا توجد ملاحظات'}\n${DateFormat('yyyy-MM-dd HH:mm').format(transaction.purchaseDate)}',
              ),
              trailing: Text(
                '+${transaction.totalAmount.toStringAsFixed(2)} ر.ي',
                style: const TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              onTap: () {
                // يمكن الانتقال إلى تفاصيل فاتورة الشراء هنا
                // context.go('/purchases/view/${transaction.id}');
              },
            ),
          );
        } else {
          // عرض سند دفع
          final receipt = transaction;
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            elevation: 1,
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.green.withValues(alpha: 0.1),
                child: const Icon(Icons.arrow_circle_down, color: Colors.green),
              ),
              title: Text(
                receipt.typeDisplayName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                '${receipt.description ?? ''}\n${DateFormat('yyyy-MM-dd HH:mm').format(receipt.transactionDate)}',
              ),
              trailing: Text(
                '-${receipt.amount.toStringAsFixed(2)} ر.ي',
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          );
        }
      },
    );
  }
}
