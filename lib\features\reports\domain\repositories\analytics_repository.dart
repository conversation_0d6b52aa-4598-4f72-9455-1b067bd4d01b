abstract class AnalyticsRepository {
  /// Get sales summary analytics
  Future<Map<String, dynamic>> getSalesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get purchases summary analytics
  Future<Map<String, dynamic>> getPurchasesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get inventory value analytics
  Future<Map<String, dynamic>> getInventoryValue();

  /// Get profit analysis
  Future<Map<String, dynamic>> getProfitAnalysis({
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Get customer analytics
  Future<Map<String, dynamic>> getCustomerAnalytics();

  /// Get daily sales trend
  Future<List<Map<String, dynamic>>> getDailySalesTrend({
    DateTime? fromDate,
    DateTime? toDate,
  });
}
