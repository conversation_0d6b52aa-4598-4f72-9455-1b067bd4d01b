import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../domain/repositories/sale_repository.dart';
import '../datasources/sale_database_service.dart';
import '../models/sale_model.dart';
import '../models/sale_item_model.dart';

class SaleRepositoryImpl implements SaleRepository {
  final SaleDatabaseService _databaseService;

  SaleRepositoryImpl(this._databaseService);

  @override
  Future<int> createSale(Sale sale, List<SaleItem> items) async {
    try {
      final saleModel = SaleModel(
        id: sale.id,
        customerId: sale.customerId,
        saleDate: sale.saleDate,
        totalAmount: sale.totalAmount,
        totalPaidAmount: sale.totalPaidAmount,
        paymentMethod: sale.paymentMethod,
        notes: sale.notes,
        status: sale.status,
      );

      final itemModels = items
          .map(
            (item) => SaleItemModel(
              id: item.id,
              saleId: item.saleId,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              itemType: item.itemType,
              description: item.description,
            ),
          )
          .toList();

      return await _databaseService.createSale(saleModel, itemModels);
    } catch (e) {
      throw Exception('Failed to create sale: $e');
    }
  }

  @override
  Future<List<Sale>> getAllSales() async {
    try {
      final saleModels = await _databaseService.getAllSales();
      return saleModels.map((model) => Sale.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get all sales: $e');
    }
  }

  @override
  Future<Sale?> getSaleById(int id) async {
    try {
      final saleModel = await _databaseService.getSaleById(id);
      return saleModel != null ? Sale.fromModel(saleModel) : null;
    } catch (e) {
      throw Exception('Failed to get sale by id: $e');
    }
  }

  @override
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      final itemModels = await _databaseService.getSaleItems(saleId);
      return itemModels.map((model) => SaleItem.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get sale items: $e');
    }
  }

  @override
  Future<void> updateSale(Sale sale) async {
    try {
      final saleModel = SaleModel(
        id: sale.id,
        customerId: sale.customerId,
        saleDate: sale.saleDate,
        totalAmount: sale.totalAmount,
        totalPaidAmount: sale.totalPaidAmount,
        paymentMethod: sale.paymentMethod,
        notes: sale.notes,
        status: sale.status,
      );

      await _databaseService.updateSale(saleModel);
    } catch (e) {
      throw Exception('Failed to update sale: $e');
    }
  }

  @override
  Future<void> updateSaleWithItems(Sale sale, List<SaleItem> items) async {
    try {
      final saleModel = SaleModel(
        id: sale.id,
        customerId: sale.customerId,
        saleDate: sale.saleDate,
        totalAmount: sale.totalAmount,
        totalPaidAmount: sale.totalPaidAmount,
        paymentMethod: sale.paymentMethod,
        notes: sale.notes,
        status: sale.status,
      );

      final itemModels = items
          .map(
            (item) => SaleItemModel(
              id: item.id,
              saleId: item.saleId,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              itemType: item.itemType,
              description: item.description,
            ),
          )
          .toList();

      await _databaseService.updateSaleWithItems(saleModel, itemModels);
    } catch (e) {
      throw Exception('Failed to update sale with items: $e');
    }
  }

  @override
  Future<void> deleteSale(int id) async {
    try {
      await _databaseService.deleteSale(id);
    } catch (e) {
      throw Exception('Failed to delete sale: $e');
    }
  }

  @override
  Future<List<Sale>> getSalesByCustomer(int customerId) async {
    try {
      final saleModels = await _databaseService.getSalesByCustomer(customerId);
      return saleModels.map((model) => Sale.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get sales by customer: $e');
    }
  }

  @override
  Future<List<Sale>> getSalesByStatus(String status) async {
    try {
      final saleModels = await _databaseService.getSalesByStatus(status);
      return saleModels.map((model) => Sale.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get sales by status: $e');
    }
  }

  @override
  Future<List<Sale>> getSalesByPaymentMethod(String paymentMethod) async {
    try {
      final saleModels = await _databaseService.getSalesByPaymentMethod(
        paymentMethod,
      );
      return saleModels.map((model) => Sale.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get sales by payment method: $e');
    }
  }

  @override
  Future<double> getTotalSalesAmountByCustomerId(int customerId) async {
    try {
      return await _databaseService.getTotalSalesAmountByCustomerId(customerId);
    } catch (e) {
      throw Exception('Failed to get total sales amount by customer: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      return await _databaseService.getSalesStatistics();
    } catch (e) {
      throw Exception('Failed to get sales statistics: $e');
    }
  }
}
