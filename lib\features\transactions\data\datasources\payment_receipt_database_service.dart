import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/payment_receipt_model.dart';

class PaymentReceiptDatabaseService {
  final DatabaseService _databaseService;

  PaymentReceiptDatabaseService(this._databaseService);

  /// Create a new payment receipt
  Future<int> createPaymentReceipt(PaymentReceiptModel paymentReceipt) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(
        'payment_receipts',
        paymentReceipt.toMap()..remove('id'), // Remove id for auto-increment
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e) {
      throw Exception('Failed to create payment receipt: $e');
    }
  }

  /// Get payment receipt by ID
  Future<PaymentReceiptModel?> getPaymentReceiptById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'payment_receipts',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return PaymentReceiptModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get payment receipt by id: $e');
    }
  }

  /// Get all payment receipts for a specific entity (customer or supplier)
  Future<List<PaymentReceiptModel>> getPaymentReceiptsByEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'payment_receipts',
        where: 'relatedEntityId = ? AND relatedEntityType = ?',
        whereArgs: [entityId, entityType],
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PaymentReceiptModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get payment receipts by entity: $e');
    }
  }

  /// Get all payment receipts for a specific invoice
  Future<List<PaymentReceiptModel>> getPaymentReceiptsByInvoice(int invoiceId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'payment_receipts',
        where: 'relatedInvoiceId = ?',
        whereArgs: [invoiceId],
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PaymentReceiptModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get payment receipts by invoice: $e');
    }
  }

  /// Get all payment receipts
  Future<List<PaymentReceiptModel>> getAllPaymentReceipts() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'payment_receipts',
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PaymentReceiptModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get all payment receipts: $e');
    }
  }

  /// Update payment receipt
  Future<void> updatePaymentReceipt(PaymentReceiptModel paymentReceipt) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'payment_receipts',
        paymentReceipt.copyWith(updatedAt: DateTime.now()).toMap(),
        where: 'id = ?',
        whereArgs: [paymentReceipt.id],
      );
    } catch (e) {
      throw Exception('Failed to update payment receipt: $e');
    }
  }

  /// Delete payment receipt
  Future<void> deletePaymentReceipt(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'payment_receipts',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to delete payment receipt: $e');
    }
  }

  /// Get payment receipts by date range
  Future<List<PaymentReceiptModel>> getPaymentReceiptsByDateRange({
    required DateTime fromDate,
    required DateTime toDate,
    String? entityType,
    int? entityId,
  }) async {
    try {
      final db = await _databaseService.database;
      
      String whereClause = 'transactionDate >= ? AND transactionDate <= ?';
      List<dynamic> whereArgs = [fromDate.toIso8601String(), toDate.toIso8601String()];
      
      if (entityType != null) {
        whereClause += ' AND relatedEntityType = ?';
        whereArgs.add(entityType);
      }
      
      if (entityId != null) {
        whereClause += ' AND relatedEntityId = ?';
        whereArgs.add(entityId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'payment_receipts',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PaymentReceiptModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get payment receipts by date range: $e');
    }
  }

  /// Get total payments for entity
  Future<double> getTotalPaymentsForEntity({
    required int entityId,
    required String entityType,
  }) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(amount) as total FROM payment_receipts WHERE relatedEntityId = ? AND relatedEntityType = ?',
        [entityId, entityType],
      );
      
      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw Exception('Failed to get total payments for entity: $e');
    }
  }

  /// Get total payments for invoice
  Future<double> getTotalPaymentsForInvoice(int invoiceId) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(amount) as total FROM payment_receipts WHERE relatedInvoiceId = ?',
        [invoiceId],
      );
      
      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw Exception('Failed to get total payments for invoice: $e');
    }
  }
}
