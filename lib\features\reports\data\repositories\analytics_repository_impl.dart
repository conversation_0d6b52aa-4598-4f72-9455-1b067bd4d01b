import '../../domain/repositories/analytics_repository.dart';
import '../datasources/analytics_database_service.dart';

class AnalyticsRepositoryImpl implements AnalyticsRepository {
  final AnalyticsDatabaseService _databaseService;

  AnalyticsRepositoryImpl(this._databaseService);

  @override
  Future<Map<String, dynamic>> getSalesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      return await _databaseService.getSalesSummary(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get sales summary: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getPurchasesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      return await _databaseService.getPurchasesSummary(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get purchases summary: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getInventoryValue() async {
    try {
      return await _databaseService.getInventoryValue();
    } catch (e) {
      throw Exception('Failed to get inventory value: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getProfitAnalysis({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      return await _databaseService.getProfitAnalysis(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get profit analysis: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getCustomerAnalytics() async {
    try {
      return await _databaseService.getCustomerAnalytics();
    } catch (e) {
      throw Exception('Failed to get customer analytics: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getDailySalesTrend({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      return await _databaseService.getDailySalesTrend(
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get daily sales trend: $e');
    }
  }
}
