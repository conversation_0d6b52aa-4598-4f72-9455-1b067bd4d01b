import '../entities/order.dart';
import '../entities/order_item.dart';
import '../repositories/order_repository.dart';

class CreateOrderUseCase {
  final OrderRepository _repository;

  CreateOrderUseCase(this._repository);

  Future<int> call(Order order, List<OrderItem> items) async {
    // Validation
    if (items.isEmpty) {
      throw Exception('Order must contain at least one item');
    }

    if (order.totalEstimatedCost < 0) {
      throw Exception('Total estimated cost cannot be negative');
    }

    if (order.status.isEmpty) {
      throw Exception('Order status cannot be empty');
    }

    // Validate items
    for (final item in items) {
      if (item.quantity <= 0) {
        throw Exception('Item quantity must be greater than 0');
      }
      if (item.estimatedUnitPrice < 0) {
        throw Exception('Item estimated unit price cannot be negative');
      }
    }

    // Validate that total matches sum of items
    final calculatedTotal = items.fold<double>(
      0.0,
      (sum, item) => sum + item.totalEstimatedCost,
    );
    
    if ((order.totalEstimatedCost - calculatedTotal).abs() > 0.01) {
      throw Exception('Total estimated cost does not match sum of items');
    }

    try {
      return await _repository.createOrder(order, items);
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }
}
