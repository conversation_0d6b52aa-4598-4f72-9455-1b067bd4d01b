import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/purchase_batch_model.dart';

class PurchaseBatchDatabaseService {
  final DatabaseService _databaseService;

  PurchaseBatchDatabaseService(this._databaseService);

  /// Add a new purchase batch to the database
  Future<int> addBatch(PurchaseBatchModel batch) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(
        'purchase_batches',
        batch.toMap()..remove('id'), // Remove id for auto-increment
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e) {
      throw Exception('Failed to add purchase batch: $e');
    }
  }

  /// Get oldest active batches for a product (FIFO order)
  Future<List<PurchaseBatchModel>> getOldestActiveBatches(int productId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchase_batches',
        where: 'productId = ? AND isActive = ? AND remainingQuantity > ?',
        whereArgs: [productId, 1, 0],
        orderBy: 'purchaseDate ASC, id ASC', // FIFO: oldest first
      );

      return List.generate(maps.length, (i) {
        return PurchaseBatchModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get oldest active batches: $e');
    }
  }

  /// Update remaining quantity of a batch and optionally its active status
  Future<void> updateBatchRemainingQuantity(
    int batchId,
    int newRemainingQuantity, {
    bool? isActive,
  }) async {
    try {
      final db = await _databaseService.database;
      
      final updateData = <String, dynamic>{
        'remainingQuantity': newRemainingQuantity,
      };
      
      // If remaining quantity is 0, automatically set isActive to false
      if (newRemainingQuantity <= 0) {
        updateData['isActive'] = 0;
      } else if (isActive != null) {
        updateData['isActive'] = isActive ? 1 : 0;
      }

      final rowsAffected = await db.update(
        'purchase_batches',
        updateData,
        where: 'id = ?',
        whereArgs: [batchId],
      );

      if (rowsAffected == 0) {
        throw Exception('No batch found with id: $batchId');
      }
    } catch (e) {
      throw Exception('Failed to update batch remaining quantity: $e');
    }
  }

  /// Get a specific batch by ID
  Future<PurchaseBatchModel?> getBatchById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchase_batches',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return PurchaseBatchModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get batch by id: $e');
    }
  }

  /// Get all batches for a product (for debugging/reporting)
  Future<List<PurchaseBatchModel>> getAllBatchesForProduct(int productId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchase_batches',
        where: 'productId = ?',
        whereArgs: [productId],
        orderBy: 'purchaseDate ASC, id ASC',
      );

      return List.generate(maps.length, (i) {
        return PurchaseBatchModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get all batches for product: $e');
    }
  }

  /// Delete all batches for a product (used when product is deleted)
  Future<void> deleteBatchesForProduct(int productId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'purchase_batches',
        where: 'productId = ?',
        whereArgs: [productId],
      );
    } catch (e) {
      throw Exception('Failed to delete batches for product: $e');
    }
  }

  /// Get total remaining quantity for a product across all active batches
  Future<int> getTotalRemainingQuantity(int productId) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        'SELECT SUM(remainingQuantity) as total FROM purchase_batches WHERE productId = ? AND isActive = 1',
        [productId],
      );
      
      return (result.first['total'] as int?) ?? 0;
    } catch (e) {
      throw Exception('Failed to get total remaining quantity: $e');
    }
  }
}
