import '../entities/supplier_account.dart';
import '../repositories/supplier_account_repository.dart';

class GetSupplierAccountStatementUseCase {
  final SupplierAccountRepository _repository;

  GetSupplierAccountStatementUseCase(this._repository);

  Future<List<SupplierAccount>> call(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    if (supplierId <= 0) {
      throw Exception('Supplier ID must be greater than 0');
    }

    if (fromDate != null && toDate != null && fromDate.isAfter(toDate)) {
      throw Exception('From date cannot be after to date');
    }

    try {
      return await _repository.getSupplierAccountStatement(
        supplierId,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      throw Exception('Failed to get supplier account statement: $e');
    }
  }
}
