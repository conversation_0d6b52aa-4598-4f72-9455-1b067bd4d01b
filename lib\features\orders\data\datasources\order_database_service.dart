import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/order_model.dart';
import '../models/order_item_model.dart';

class OrderDatabaseService {
  final DatabaseService _databaseService;

  OrderDatabaseService(this._databaseService);

  /// Create a new order with its items
  Future<int> createOrder(OrderModel order, List<OrderItemModel> items) async {
    try {
      final db = await _databaseService.database;
      
      return await db.transaction((txn) async {
        // Insert order
        final orderId = await txn.insert(
          'orders',
          order.toMap()..remove('id'), // Remove id for auto-increment
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Insert order items
        for (final item in items) {
          await txn.insert(
            'order_items',
            item.copyWith(orderId: orderId).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        return orderId;
      });
    } catch (e) {
      throw Exception('Failed to create order: $e');
    }
  }

  /// Get all orders
  Future<List<OrderModel>> getAllOrders() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        orderBy: 'orderDate DESC, id DESC', // Most recent first
      );

      return List.generate(maps.length, (i) {
        return OrderModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get orders: $e');
    }
  }

  /// Get order by ID
  Future<OrderModel?> getOrderById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return OrderModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get order by id: $e');
    }
  }

  /// Get order items by order ID
  Future<List<OrderItemModel>> getOrderItems(int orderId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'order_items',
        where: 'orderId = ?',
        whereArgs: [orderId],
        orderBy: 'id ASC',
      );

      return List.generate(maps.length, (i) {
        return OrderItemModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get order items: $e');
    }
  }

  /// Update order
  Future<void> updateOrder(OrderModel order) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'orders',
        order.toMap(),
        where: 'id = ?',
        whereArgs: [order.id],
      );
    } catch (e) {
      throw Exception('Failed to update order: $e');
    }
  }

  /// Update order with items
  Future<void> updateOrderWithItems(
    OrderModel order,
    List<OrderItemModel> items,
  ) async {
    try {
      final db = await _databaseService.database;
      
      await db.transaction((txn) async {
        // Update order
        await txn.update(
          'orders',
          order.toMap(),
          where: 'id = ?',
          whereArgs: [order.id],
        );

        // Delete existing items
        await txn.delete(
          'order_items',
          where: 'orderId = ?',
          whereArgs: [order.id],
        );

        // Insert new items
        for (final item in items) {
          await txn.insert(
            'order_items',
            item.copyWith(orderId: order.id!).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
    } catch (e) {
      throw Exception('Failed to update order with items: $e');
    }
  }

  /// Delete order
  Future<void> deleteOrder(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'orders',
        where: 'id = ?',
        whereArgs: [id],
      );
      // Order items will be deleted automatically due to CASCADE
    } catch (e) {
      throw Exception('Failed to delete order: $e');
    }
  }

  /// Get orders by status
  Future<List<OrderModel>> getOrdersByStatus(String status) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'status = ?',
        whereArgs: [status],
        orderBy: 'orderDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return OrderModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get orders by status: $e');
    }
  }

  /// Get order statistics
  Future<Map<String, dynamic>> getOrderStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total orders count
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM orders'
      );
      final totalCount = countResult.first['count'] as int;

      // Get total estimated cost
      final costResult = await db.rawQuery(
        'SELECT SUM(totalEstimatedCost) as totalCost FROM orders'
      );
      final totalCost = (costResult.first['totalCost'] as num?)?.toDouble() ?? 0.0;

      // Get counts by status
      final statusResult = await db.rawQuery('''
        SELECT status, COUNT(*) as count 
        FROM orders 
        GROUP BY status
      ''');

      final statusCounts = <String, int>{};
      for (final row in statusResult) {
        statusCounts[row['status'] as String] = row['count'] as int;
      }

      return {
        'totalCount': totalCount,
        'totalCost': totalCost,
        'pendingCount': statusCounts['pending'] ?? 0,
        'completedCount': statusCounts['completed'] ?? 0,
        'cancelledCount': statusCounts['cancelled'] ?? 0,
      };
    } catch (e) {
      throw Exception('Failed to get order statistics: $e');
    }
  }
}
