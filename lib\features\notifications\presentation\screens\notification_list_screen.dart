import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../domain/entities/app_notification.dart';
import '../providers/notification_provider.dart';
import '../../../../shared_widgets/wrappers.dart';

class NotificationListScreen extends StatefulWidget {
  const NotificationListScreen({super.key});

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationProvider>().fetchNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'التنبيهات',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('التنبيهات'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                context.read<NotificationProvider>().fetchNotifications();
              },
            ),
          ],
        ),
        body: Consumer<NotificationProvider>(
          builder: (context, notificationProvider, child) {
            if (notificationProvider.isLoading && notificationProvider.notifications.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (notificationProvider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      notificationProvider.errorMessage!,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        notificationProvider.fetchNotifications();
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            if (notificationProvider.notifications.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.notifications_none,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد تنبيهات',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'ستظهر التنبيهات هنا عند توفرها',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Statistics Card
                _buildStatisticsCard(notificationProvider),
                
                // Notifications List
                Expanded(
                  child: ListView.builder(
                    itemCount: notificationProvider.notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notificationProvider.notifications[index];
                      return _buildNotificationCard(notification, notificationProvider);
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(NotificationProvider provider) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'المجموع',
                '${provider.notifications.length}',
                Icons.notifications,
                Colors.blue,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'غير مقروءة',
                '${provider.unreadCount}',
                Icons.notifications_active,
                Colors.orange,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'تم التعديل',
                '${provider.notifications.where((n) => n.isActionTaken).length}',
                Icons.check_circle,
                Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(AppNotification notification, NotificationProvider provider) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: notification.isRead ? null : Colors.blue.withValues(alpha: 0.05),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getNotificationColor(notification.type).withValues(alpha: 0.1),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: _getNotificationColor(notification.type),
          ),
        ),
        title: Text(
          notification.message,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${notification.typeDisplayName} • ${DateFormat('yyyy-MM-dd HH:mm').format(notification.date)}',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(notification).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    notification.statusText,
                    style: TextStyle(
                      fontSize: 10,
                      color: _getStatusColor(notification),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: notification.hasAction
            ? IconButton(
                icon: Icon(
                  notification.isActionTaken ? Icons.check_circle : Icons.arrow_forward,
                  color: notification.isActionTaken ? Colors.green : Colors.blue,
                ),
                onPressed: notification.isActionTaken
                    ? null
                    : () => _handleNotificationAction(notification, provider),
              )
            : null,
        onTap: () => _handleNotificationTap(notification, provider),
      ),
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'low_stock_alert':
        return Icons.inventory_2;
      case 'price_change':
        return Icons.price_change;
      case 'order_reminder':
        return Icons.shopping_cart;
      case 'system_notification':
        return Icons.info;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'low_stock_alert':
        return Colors.red;
      case 'price_change':
        return Colors.orange;
      case 'order_reminder':
        return Colors.blue;
      case 'system_notification':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(AppNotification notification) {
    if (notification.isActionTaken) {
      return Colors.green;
    } else if (notification.isRead) {
      return Colors.blue;
    } else {
      return Colors.orange;
    }
  }

  void _handleNotificationTap(AppNotification notification, NotificationProvider provider) {
    if (!notification.isRead) {
      provider.markAsRead(notification.id!);
    }
  }

  void _handleNotificationAction(AppNotification notification, NotificationProvider provider) {
    if (notification.suggestedActionRoute != null) {
      // Mark action as taken
      provider.markActionTaken(notification.id!);
      
      // Navigate to suggested route
      context.go(notification.suggestedActionRoute!);
    }
  }
}
