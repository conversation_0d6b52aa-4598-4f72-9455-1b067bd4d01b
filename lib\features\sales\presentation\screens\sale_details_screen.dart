import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/sale_provider.dart';
import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../../products/domain/usecases/get_product_by_id.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../customers/domain/entities/customer.dart';
import 'package:intl/intl.dart';

class SaleDetailsScreen extends StatefulWidget {
  final int saleId;

  const SaleDetailsScreen({super.key, required this.saleId});

  @override
  State<SaleDetailsScreen> createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  Sale? sale;
  List<SaleItem> saleItems = [];
  Customer? customer;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSale();
  }

  Future<void> _loadSale() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final saleProvider = context.read<SaleProvider>();
      final customerProvider = context.read<CustomerProvider>();

      // تحميل الفاتورة
      final loadedSale = await saleProvider.getSaleById(widget.saleId);
      if (loadedSale == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      // تحميل عناصر الفاتورة
      final loadedSaleItems = await saleProvider.getSaleItems(widget.saleId);

      // تحميل بيانات العميل إذا كان موجوداً
      Customer? loadedCustomer;
      if (loadedSale.customerId != null) {
        loadedCustomer = await customerProvider.getCustomerById(
          loadedSale.customerId!,
        );
      }

      if (mounted) {
        setState(() {
          sale = loadedSale;
          saleItems = loadedSaleItems;
          customer = loadedCustomer;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = 'خطأ في تحميل بيانات الفاتورة: ${e.toString()}';
          isLoading = false;
        });
      }
    }
  }

  // دوال وهمية للطباعة والمشاركة
  void _printInvoice() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('جارٍ إعداد الطباعة...'),
        backgroundColor: Colors.blue.shade600,
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  void _shareInvoice() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('جارٍ إعداد المشاركة...'),
        backgroundColor: Colors.green.shade600,
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  void _exportToPdf() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('جارٍ تصدير الفاتورة كـ PDF قريباً...'),
        backgroundColor: Colors.red.shade600,
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  Future<void> _deleteSale() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await context.read<SaleProvider>().deleteSale(widget.saleId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الفاتورة: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: sale != null ? 'فاتورة رقم ${sale!.id}' : 'تفاصيل الفاتورة',
      actions: [
        if (sale != null) ...[
          // زر الطباعة المحسن
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.shade200,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.print, color: Colors.white),
              onPressed: _printInvoice,
              tooltip: 'طباعة',
            ),
          ),

          // زر المشاركة المحسن
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.shade200,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.share, color: Colors.white),
              onPressed: _shareInvoice,
              tooltip: 'مشاركة',
            ),
          ),

          // زر تصدير PDF الجديد
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.red.shade400, Colors.red.shade600],
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.shade200,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.picture_as_pdf, color: Colors.white),
              onPressed: _exportToPdf,
              tooltip: 'تصدير PDF',
            ),
          ),

          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => context.go('/sales/edit/${widget.saleId}'),
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteSale,
            tooltip: 'حذف',
          ),
        ],
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSale,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (sale == null) {
      return const Center(child: Text('لم يتم العثور على الفاتورة'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialSummaryCard(),
          const SizedBox(height: 16),
          _buildSaleHeaderCard(),
          const SizedBox(height: 16),
          _buildSaleItemsCard(),
          const SizedBox(height: 16),
          _buildCustomerInfoCard(),
          const SizedBox(height: 16),
          _buildPaymentInfoCard(),
          const SizedBox(height: 16),
          _buildStatusInfoCard(),
          const SizedBox(height: 16),
          _buildAdditionalInfoCard(),
          const SizedBox(height: 16),
          if (sale != null && !sale!.isFullyPaid) _buildAddPaymentButton(),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    if (sale == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: Colors.blue.shade700,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الملخص المالي',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildFinancialItem(
                      'الإجمالي',
                      '${sale!.totalAmount.toStringAsFixed(2)} ر.ي',
                      Icons.receipt_long,
                      Colors.blue.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildFinancialItem(
                      'المدفوع',
                      '${sale!.totalPaidAmount.toStringAsFixed(2)} ر.ي',
                      Icons.payments,
                      Colors.green.shade600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildFinancialItem(
                      'المتبقي',
                      '${sale!.dueAmount.toStringAsFixed(2)} ر.ي',
                      Icons.pending_actions,
                      sale!.dueAmount > 0
                          ? Colors.red.shade600
                          : Colors.green.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: sale!.isFullyPaid
                      ? Colors.green.shade100
                      : sale!.isPartiallyPaid
                      ? Colors.orange.shade100
                      : Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      sale!.isFullyPaid
                          ? Icons.check_circle
                          : sale!.isPartiallyPaid
                          ? Icons.schedule
                          : Icons.pending,
                      color: sale!.isFullyPaid
                          ? Colors.green.shade700
                          : sale!.isPartiallyPaid
                          ? Colors.orange.shade700
                          : Colors.red.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      sale!.paymentStatusText,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: sale!.isFullyPaid
                            ? Colors.green.shade700
                            : sale!.isPartiallyPaid
                            ? Colors.orange.shade700
                            : Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleHeaderCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, Colors.grey.shade50],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // ترويسة الشركة
              Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    Text(
                      'أُسامة ماركت',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                            fontSize: 28,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'محطة المسعودي - الشارع العام',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'رقم الهاتف: 739740717',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // عنوان الفاتورة
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 20,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.shade600,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Text(
                  'فاتورة مبيعات رقم ${sale!.id}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // تفاصيل الفاتورة في جدول
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Table(
                  columnWidths: const {
                    0: FlexColumnWidth(1),
                    1: FlexColumnWidth(1),
                  },
                  children: [
                    // صف التاريخ والعميل
                    TableRow(
                      decoration: BoxDecoration(color: Colors.grey.shade100),
                      children: [
                        _buildTableCell(
                          'التاريخ',
                          dateFormat.format(sale!.saleDate),
                          isHeader: true,
                        ),
                        _buildTableCell(
                          'العميل',
                          customer?.name ?? 'عميل عابر',
                          isHeader: true,
                        ),
                      ],
                    ),
                    // صف طريقة الدفع والحالة
                    TableRow(
                      children: [
                        _buildTableCell(
                          'طريقة الدفع',
                          sale!.paymentMethodDisplayName,
                        ),
                        _buildTableCell(
                          'حالة الدفع',
                          sale!.paymentStatusText,
                          valueColor: sale!.paymentStatus == 'paid'
                              ? Colors.green
                              : Colors.orange,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // المبلغ الإجمالي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200, width: 2),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المبلغ الإجمالي:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade800,
                      ),
                    ),
                    Text(
                      '${sale!.totalAmount.toStringAsFixed(2)} ر.ي',
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTableCell(
    String label,
    String value, {
    bool isHeader = false,
    Color? valueColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isHeader ? FontWeight.bold : FontWeight.w500,
              color: valueColor ?? (isHeader ? Colors.black87 : Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleItemsCard() {
    if (saleItems.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.shopping_cart_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد عناصر في هذه الفاتورة',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(Icons.list_alt, color: Colors.blue.shade700, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'عناصر الفاتورة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // جدول رأس العناصر
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Table(
                columnWidths: const {
                  0: FixedColumnWidth(40),
                  1: FlexColumnWidth(3),
                  2: FlexColumnWidth(1),
                  3: FlexColumnWidth(1),
                  4: FlexColumnWidth(1),
                },
                children: [
                  TableRow(
                    children: [
                      _buildItemHeaderCell('#'),
                      _buildItemHeaderCell('البيان'),
                      _buildItemHeaderCell('الكمية'),
                      _buildItemHeaderCell('السعر'),
                      _buildItemHeaderCell('القيمة'),
                    ],
                  ),
                ],
              ),
            ),

            // عناصر الفاتورة
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Column(
                children: saleItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return _buildSaleItemTableRow(item, index);
                }).toList(),
              ),
            ),

            const SizedBox(height: 16),

            // تذييل الفاتورة
            _buildInvoiceFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildItemHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.grey[700],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildSaleItemTableRow(SaleItem item, int index) {
    return FutureBuilder<String>(
      future: _getProductName(item),
      builder: (context, snapshot) {
        final productName = snapshot.data ?? 'جاري التحميل...';

        return Container(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
          ),
          child: Table(
            columnWidths: const {
              0: FixedColumnWidth(40),
              1: FlexColumnWidth(3),
              2: FlexColumnWidth(1),
              3: FlexColumnWidth(1),
              4: FlexColumnWidth(1),
            },
            children: [
              TableRow(
                children: [
                  _buildItemDataCell('${index + 1}'),
                  _buildItemDataCell(productName, isProductName: true),
                  _buildItemDataCell((item.quantity ?? 1).toString()),
                  _buildItemDataCell(item.unitPrice.toStringAsFixed(2)),
                  _buildItemDataCell(
                    item.totalPrice.toStringAsFixed(2),
                    isAmount: true,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildItemDataCell(
    String text, {
    bool isProductName = false,
    bool isAmount = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: isAmount ? FontWeight.bold : FontWeight.normal,
          color: isAmount ? Colors.green.shade700 : Colors.black87,
        ),
        textAlign: isProductName ? TextAlign.start : TextAlign.center,
      ),
    );
  }

  Widget _buildInvoiceFooter() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade300, width: 2),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // المبلغ الإجمالي
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ الإجمالي:',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade800,
                  ),
                ),
                Text(
                  '${sale!.totalAmount.toStringAsFixed(2)} ر.ي',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),

            const Divider(color: Colors.green, thickness: 1),

            // معلومات الدفع
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'طريقة الدفع:',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[700]),
                    ),
                    Text(
                      sale!.paymentMethodDisplayName,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'حالة الدفع:',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[700]),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: sale!.paymentStatus == 'paid'
                            ? Colors.green
                            : Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        sale!.paymentStatusText,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<String> _getProductName(SaleItem item) async {
    if (item.isRetailGoodsSummary) {
      return item.description ?? 'بند بقالة';
    }

    if (item.isWholesaleProduct && item.productId != null) {
      try {
        final getProductByIdUseCase = GetIt.instance<GetProductByIdUseCase>();
        final product = await getProductByIdUseCase.call(item.productId!);
        return product?.name ?? 'منتج رقم ${item.productId}';
      } catch (e) {
        return 'منتج رقم ${item.productId}';
      }
    }

    return item.description ?? 'بند غير محدد';
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.person,
              'العميل',
              customer?.name ?? 'عميل عابر',
            ),
            if (customer?.phone != null && customer!.phone!.isNotEmpty)
              _buildInfoRow(Icons.phone, 'رقم الهاتف', customer!.phone!),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.payment,
              'طريقة الدفع',
              sale!.paymentMethodDisplayName,
            ),
            _buildInfoRow(
              Icons.account_balance_wallet,
              'حالة الدفع',
              sale!.paymentStatusText,
              valueColor: sale!.paymentStatus == 'paid'
                  ? Colors.green
                  : Colors.orange,
            ),
            _buildInfoRow(
              Icons.money,
              'المبلغ الإجمالي',
              '${sale!.totalAmount.toStringAsFixed(2)} ر.ي',
              valueColor: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.info,
              'الحالة',
              sale!.statusDisplayName,
              valueColor: sale!.status == 'completed'
                  ? Colors.green
                  : Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (sale!.createdAt != null)
              _buildInfoRow(
                Icons.calendar_today,
                'تاريخ الإنشاء',
                dateFormat.format(sale!.createdAt!),
              ),
            if (sale!.updatedAt != null)
              _buildInfoRow(
                Icons.update,
                'آخر تحديث',
                dateFormat.format(sale!.updatedAt!),
              ),
            if (sale!.notes != null && sale!.notes!.isNotEmpty)
              _buildInfoRow(Icons.note, 'ملاحظات', sale!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: valueColor,
                    fontWeight: valueColor != null ? FontWeight.bold : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddPaymentButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: () {
          if (sale != null && sale!.customerId != null) {
            context.push(
              '/transactions/receipts/new',
              extra: {
                'relatedEntityType': 'customer',
                'relatedEntityId': sale!.customerId,
                'relatedInvoiceId': sale!.id,
                'amount': sale!.dueAmount, // تعبئة المبلغ المتبقي تلقائياً
              },
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لا يمكن إضافة دفعة لفاتورة عميل عابر'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        },
        icon: const Icon(Icons.payment, color: Colors.white),
        label: Text(
          'إضافة دفعة (${sale!.dueAmount.toStringAsFixed(2)} ر.ي)',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade600,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
      ),
    );
  }
}
