import '../../domain/entities/expense.dart';
import '../../domain/repositories/expense_repository.dart';
import '../datasources/expense_database_service.dart';
import '../models/expense_model.dart';

class ExpenseRepositoryImpl implements ExpenseRepository {
  final ExpenseDatabaseService _databaseService;

  ExpenseRepositoryImpl(this._databaseService);

  @override
  Future<List<Expense>> getAllExpenses() async {
    try {
      final expenseModels = await _databaseService.getAllExpenses();
      return expenseModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to get expenses: $e');
    }
  }

  @override
  Future<Expense?> getExpenseById(int id) async {
    try {
      final expenseModel = await _databaseService.getExpenseById(id);
      if (expenseModel != null) {
        return _modelToEntity(expenseModel);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get expense by id: $e');
    }
  }

  @override
  Future<int> createExpense(Expense expense) async {
    try {
      final expenseModel = _entityToModel(expense);
      return await _databaseService.createExpense(expenseModel);
    } catch (e) {
      throw Exception('Failed to create expense: $e');
    }
  }

  @override
  Future<void> updateExpense(Expense expense) async {
    try {
      final expenseModel = _entityToModel(expense);
      await _databaseService.updateExpense(expenseModel);
    } catch (e) {
      throw Exception('Failed to update expense: $e');
    }
  }

  @override
  Future<void> deleteExpense(int id) async {
    try {
      await _databaseService.deleteExpense(id);
    } catch (e) {
      throw Exception('Failed to delete expense: $e');
    }
  }

  @override
  Future<List<Expense>> getExpensesByCategory(String category) async {
    try {
      final expenseModels = await _databaseService.getExpensesByCategory(category);
      return expenseModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to get expenses by category: $e');
    }
  }

  @override
  Future<List<Expense>> getExpensesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final expenseModels = await _databaseService.getExpensesByDateRange(startDate, endDate);
      return expenseModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to get expenses by date range: $e');
    }
  }

  @override
  Future<double> getTotalExpensesByCategory(String category) async {
    try {
      return await _databaseService.getTotalExpensesByCategory(category);
    } catch (e) {
      throw Exception('Failed to get total expenses by category: $e');
    }
  }

  @override
  Future<double> getTotalExpensesByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      return await _databaseService.getTotalExpensesByDateRange(startDate, endDate);
    } catch (e) {
      throw Exception('Failed to get total expenses by date range: $e');
    }
  }

  Expense _modelToEntity(ExpenseModel model) {
    return Expense(
      id: model.id,
      description: model.description,
      amount: model.amount,
      category: model.category,
      expenseDate: model.expenseDate,
      notes: model.notes,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  ExpenseModel _entityToModel(Expense entity) {
    return ExpenseModel(
      id: entity.id,
      description: entity.description,
      amount: entity.amount,
      category: entity.category,
      expenseDate: entity.expenseDate,
      notes: entity.notes,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
