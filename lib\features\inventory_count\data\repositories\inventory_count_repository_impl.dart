import '../../domain/entities/inventory_count.dart';
import '../../domain/repositories/inventory_count_repository.dart';
import '../datasources/inventory_count_database_service.dart';
import '../models/inventory_count_model.dart';

class InventoryCountRepositoryImpl implements InventoryCountRepository {
  final InventoryCountDatabaseService _databaseService;

  InventoryCountRepositoryImpl(this._databaseService);

  @override
  Future<int> createInventoryCount(InventoryCount inventoryCount) async {
    try {
      final inventoryCountModel = InventoryCountModel(
        id: inventoryCount.id,
        productId: inventoryCount.productId,
        countDate: inventoryCount.countDate,
        countedWarehouseQuantity: inventoryCount.countedWarehouseQuantity,
        countedStoreQuantity: inventoryCount.countedStoreQuantity,
        systemWarehouseQuantity: inventoryCount.systemWarehouseQuantity,
        systemStoreQuantity: inventoryCount.systemStoreQuantity,
        warehouseDifference: inventoryCount.warehouseDifference,
        storeDifference: inventoryCount.storeDifference,
        notes: inventoryCount.notes,
      );

      return await _databaseService.createInventoryCount(inventoryCountModel);
    } catch (e) {
      throw Exception('Failed to create inventory count: $e');
    }
  }

  @override
  Future<List<InventoryCount>> getAllInventoryCounts() async {
    try {
      final inventoryCountModels = await _databaseService.getAllInventoryCounts();
      return inventoryCountModels.map((model) => InventoryCount.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get all inventory counts: $e');
    }
  }

  @override
  Future<InventoryCount?> getInventoryCountById(int id) async {
    try {
      final inventoryCountModel = await _databaseService.getInventoryCountById(id);
      return inventoryCountModel != null ? InventoryCount.fromModel(inventoryCountModel) : null;
    } catch (e) {
      throw Exception('Failed to get inventory count by id: $e');
    }
  }

  @override
  Future<List<InventoryCount>> getInventoryCountsByProduct(int productId) async {
    try {
      final inventoryCountModels = await _databaseService.getInventoryCountsByProduct(productId);
      return inventoryCountModels.map((model) => InventoryCount.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get inventory counts by product: $e');
    }
  }

  @override
  Future<List<InventoryCount>> getInventoryCountsByDateRange(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final inventoryCountModels = await _databaseService.getInventoryCountsByDateRange(fromDate, toDate);
      return inventoryCountModels.map((model) => InventoryCount.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get inventory counts by date range: $e');
    }
  }

  @override
  Future<void> updateInventoryCount(InventoryCount inventoryCount) async {
    try {
      final inventoryCountModel = InventoryCountModel(
        id: inventoryCount.id,
        productId: inventoryCount.productId,
        countDate: inventoryCount.countDate,
        countedWarehouseQuantity: inventoryCount.countedWarehouseQuantity,
        countedStoreQuantity: inventoryCount.countedStoreQuantity,
        systemWarehouseQuantity: inventoryCount.systemWarehouseQuantity,
        systemStoreQuantity: inventoryCount.systemStoreQuantity,
        warehouseDifference: inventoryCount.warehouseDifference,
        storeDifference: inventoryCount.storeDifference,
        notes: inventoryCount.notes,
      );

      await _databaseService.updateInventoryCount(inventoryCountModel);
    } catch (e) {
      throw Exception('Failed to update inventory count: $e');
    }
  }

  @override
  Future<void> deleteInventoryCount(int id) async {
    try {
      await _databaseService.deleteInventoryCount(id);
    } catch (e) {
      throw Exception('Failed to delete inventory count: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getInventoryCountStatistics() async {
    try {
      return await _databaseService.getInventoryCountStatistics();
    } catch (e) {
      throw Exception('Failed to get inventory count statistics: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getProductsNeedingCount({int daysSinceLastCount = 30}) async {
    try {
      return await _databaseService.getProductsNeedingCount(daysSinceLastCount: daysSinceLastCount);
    } catch (e) {
      throw Exception('Failed to get products needing count: $e');
    }
  }
}
