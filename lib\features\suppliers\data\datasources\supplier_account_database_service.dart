import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/supplier_account_model.dart';

class SupplierAccountDatabaseService {
  final DatabaseService _databaseService;

  SupplierAccountDatabaseService(this._databaseService);

  /// Add a new supplier account entry
  Future<int> addSupplierAccountEntry(SupplierAccountModel entry) async {
    try {
      final db = await _databaseService.database;
      final entryMap = entry.toMap();
      entryMap.remove('id'); // Remove id for auto-increment

      return await db.insert(
        'supplier_accounts',
        entryMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to add supplier account entry: $e');
    }
  }

  /// Get supplier account statement
  Future<List<SupplierAccountModel>> getSupplierAccountStatement(
    int supplierId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseService.database;
      
      String whereClause = 'supplierId = ?';
      List<dynamic> whereArgs = [supplierId];

      if (fromDate != null) {
        whereClause += ' AND transactionDate >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += ' AND transactionDate <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'supplier_accounts',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transactionDate ASC, id ASC',
      );

      return List.generate(maps.length, (i) {
        return SupplierAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get supplier account statement: $e');
    }
  }

  /// Get all supplier account entries
  Future<List<SupplierAccountModel>> getAllSupplierAccountEntries() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'supplier_accounts',
        orderBy: 'transactionDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return SupplierAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get all supplier account entries: $e');
    }
  }

  /// Delete supplier account entry
  Future<void> deleteSupplierAccountEntry(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'supplier_accounts',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to delete supplier account entry: $e');
    }
  }

  /// Delete all entries for a supplier
  Future<void> deleteAllEntriesForSupplier(int supplierId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'supplier_accounts',
        where: 'supplierId = ?',
        whereArgs: [supplierId],
      );
    } catch (e) {
      throw Exception('Failed to delete all entries for supplier: $e');
    }
  }
}
