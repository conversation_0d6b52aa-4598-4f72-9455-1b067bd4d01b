import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:market/my_app.dart';
import 'package:market/core/di/di_container.dart';
import 'package:market/core/database/database_service.dart';

import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/presentation/providers/category_provider.dart';
import 'package:market/features/products/presentation/providers/internal_transfer_provider.dart';
import 'package:market/features/orders/presentation/providers/order_provider.dart';
import 'package:market/features/notifications/presentation/providers/notification_provider.dart';
import 'package:market/features/activities/presentation/providers/activity_provider.dart';
import 'package:market/features/sales/presentation/providers/sale_provider.dart';
import 'package:market/features/purchases/presentation/providers/purchase_provider.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';
import 'package:market/features/inventory_count/presentation/providers/inventory_count_provider.dart';
import 'package:market/features/reports/presentation/providers/analytics_provider.dart';
import 'package:market/features/expenses/presentation/providers/expense_provider.dart';
import 'package:market/features/transactions/presentation/providers/payment_receipt_provider.dart';
import 'package:market/core/backup/backup_service.dart';

// GlobalKey لإعادة تشغيل التطبيق
final GlobalKey<RestartAppState> restartAppKey = GlobalKey<RestartAppState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Setup dependency injection
  await setupDependencyInjection();

  // Initialize database
  await DatabaseService.instance.database;

  // Initialize backup service
  await BackupService.initializeWorkmanager();
  await BackupService.instance.setupAutomaticBackup();

  runApp(
    RestartApp(
      key: restartAppKey,
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<ProductProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<CategoryProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<InternalTransferProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<OrderProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<NotificationProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<ActivityProvider>(),
          ),
          ChangeNotifierProvider(create: (_) => GetIt.instance<SaleProvider>()),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<PurchaseProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<CustomerProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<SupplierProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<InventoryCountProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<AnalyticsProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<ExpenseProvider>(),
          ),
          ChangeNotifierProvider(
            create: (_) => GetIt.instance<PaymentReceiptProvider>(),
          ),
        ],
        child: const MyApp(),
      ),
    ),
  );
}

/// Widget لإعادة تشغيل التطبيق بأمان
class RestartApp extends StatefulWidget {
  const RestartApp({super.key, required this.child});

  final Widget child;

  /// دالة لإعادة تشغيل التطبيق من أي مكان
  static Future<void> restart() async {
    // إعادة تهيئة dependency injection
    await GetIt.instance.reset();
    await setupDependencyInjection();

    // إعادة تهيئة قاعدة البيانات
    await DatabaseService.instance.database;

    // إعادة تشغيل التطبيق
    restartAppKey.currentState?.restartApp();
  }

  @override
  State<RestartApp> createState() => RestartAppState();
}

class RestartAppState extends State<RestartApp> {
  Key _key = UniqueKey();

  void restartApp() {
    setState(() {
      _key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(key: _key, child: widget.child);
  }
}
