import '../../domain/entities/customer_account.dart';
import '../../domain/repositories/customer_account_repository.dart';
import '../datasources/customer_account_database_service.dart';
import '../models/customer_account_model.dart';

class CustomerAccountRepositoryImpl implements CustomerAccountRepository {
  final CustomerAccountDatabaseService _databaseService;

  CustomerAccountRepositoryImpl(this._databaseService);

  @override
  Future<int> addCustomerAccountEntry(CustomerAccount entry) async {
    try {
      final entryModel = CustomerAccountModel(
        id: entry.id,
        customerId: entry.customerId,
        transactionDate: entry.transactionDate,
        type: entry.type,
        amount: entry.amount,
        description: entry.description,
        relatedInvoiceId: entry.relatedInvoiceId,
        isPaid: entry.isPaid,
      );

      return await _databaseService.addCustomerAccountEntry(entryModel);
    } catch (e) {
      throw Exception('Failed to add customer account entry: $e');
    }
  }

  @override
  Future<List<CustomerAccount>> getCustomerAccountStatement(
    int customerId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final entryModels = await _databaseService.getCustomerAccountStatement(
        customerId,
        fromDate: fromDate,
        toDate: toDate,
      );
      return entryModels
          .map((model) => CustomerAccount.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get customer account statement: $e');
    }
  }

  @override
  Future<double> getCustomerBalance(int customerId) async {
    try {
      return await _databaseService.getCustomerBalance(customerId);
    } catch (e) {
      throw Exception('Failed to get customer balance: $e');
    }
  }

  @override
  Future<List<CustomerAccount>> getUnpaidEntries(int customerId) async {
    try {
      final entryModels = await _databaseService.getUnpaidEntries(customerId);
      return entryModels
          .map((model) => CustomerAccount.fromModel(model))
          .toList();
    } catch (e) {
      throw Exception('Failed to get unpaid entries: $e');
    }
  }

  @override
  Future<void> markEntryAsPaid(int entryId) async {
    try {
      await _databaseService.markEntryAsPaid(entryId);
    } catch (e) {
      throw Exception('Failed to mark entry as paid: $e');
    }
  }

  @override
  Future<void> updateCustomerAccountEntry(CustomerAccount entry) async {
    try {
      final entryModel = CustomerAccountModel(
        id: entry.id,
        customerId: entry.customerId,
        transactionDate: entry.transactionDate,
        type: entry.type,
        amount: entry.amount,
        description: entry.description,
        relatedInvoiceId: entry.relatedInvoiceId,
        isPaid: entry.isPaid,
      );

      await _databaseService.updateCustomerAccountEntry(entryModel);
    } catch (e) {
      throw Exception('Failed to update customer account entry: $e');
    }
  }

  @override
  Future<void> deleteCustomerAccountEntry(int entryId) async {
    try {
      await _databaseService.deleteCustomerAccountEntry(entryId);
    } catch (e) {
      throw Exception('Failed to delete customer account entry: $e');
    }
  }

  @override
  Future<double> getTotalPaymentsReceivedForCustomer(int customerId) async {
    try {
      return await _databaseService.getTotalPaymentsReceivedForCustomer(
        customerId,
      );
    } catch (e) {
      throw Exception('Failed to get total payments received for customer: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getCustomerAccountStatistics(
    int customerId,
  ) async {
    try {
      return await _databaseService.getCustomerAccountStatistics(customerId);
    } catch (e) {
      throw Exception('Failed to get customer account statistics: $e');
    }
  }
}
