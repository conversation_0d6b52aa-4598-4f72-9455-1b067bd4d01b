import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/presentation/providers/category_provider.dart';

class ProductFormScreen extends StatefulWidget {
  final int? productId;

  const ProductFormScreen({super.key, this.productId});

  @override
  State<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _unitController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _wholesalePriceController = TextEditingController();
  final _retailPriceController = TextEditingController();
  final _minStockController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _warehouseQuantityController = TextEditingController();
  final _storeQuantityController = TextEditingController();

  String? _selectedCategory;
  bool _isLoading = false;
  bool _isEditMode = false;

  // Helper method to safely get available categories
  Set<String> _getAvailableCategories(CategoryProvider categoryProvider) {
    return categoryProvider.categories
        .map((category) => category.name.trim())
        .where((name) => name.isNotEmpty)
        .toSet();
  }

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.productId != null;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CategoryProvider>().fetchCategories();
      if (_isEditMode) {
        _loadProduct();
      }
    });
  }

  Future<void> _loadProduct() async {
    if (widget.productId == null) return;

    final productProvider = context.read<ProductProvider>();
    await productProvider.getProductById(widget.productId!);
    final product = productProvider.selectedProduct;

    if (product != null) {
      _nameController.text = product.name;
      _descriptionController.text = product.description ?? '';
      _unitController.text = product.unit;
      _purchasePriceController.text =
          product.lastPurchasePrice?.toString() ?? '';
      _wholesalePriceController.text = product.wholesalePrice.toString();
      _retailPriceController.text = product.retailPrice.toString();
      _minStockController.text = product.minStockQuantity.toString();
      _barcodeController.text = product.barcode ?? '';
      _warehouseQuantityController.text = product.warehouseQuantity.toString();
      _storeQuantityController.text = product.storeQuantity.toString();
      _selectedCategory = product.category.trim();
      setState(() {});
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _unitController.dispose();
    _purchasePriceController.dispose();
    _wholesalePriceController.dispose();
    _retailPriceController.dispose();
    _minStockController.dispose();
    _barcodeController.dispose();
    _warehouseQuantityController.dispose();
    _storeQuantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _isEditMode ? 'تعديل منتج' : 'إضافة منتج جديد',
      child: Consumer2<ProductProvider, CategoryProvider>(
        builder: (context, productProvider, categoryProvider, child) {
          if (categoryProvider.isLoading &&
              categoryProvider.categories.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          return Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Product Name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المنتج *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم المنتج';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // Category Dropdown
                DropdownButtonFormField<String>(
                  value:
                      _getAvailableCategories(
                        categoryProvider,
                      ).contains(_selectedCategory?.trim())
                      ? _selectedCategory?.trim()
                      : null,
                  decoration: const InputDecoration(
                    labelText: 'الفئة *',
                    border: OutlineInputBorder(),
                  ),
                  items: _getAvailableCategories(categoryProvider).map((
                    categoryName,
                  ) {
                    return DropdownMenuItem(
                      value: categoryName,
                      child: Text(categoryName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار الفئة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Unit
                TextFormField(
                  controller: _unitController,
                  decoration: const InputDecoration(
                    labelText: 'الوحدة *',
                    hintText: 'مثال: قطعة، كيلو، لتر',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الوحدة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Last Purchase Price
                TextFormField(
                  controller: _purchasePriceController,
                  decoration: const InputDecoration(
                    labelText: 'آخر سعر شراء',
                    border: OutlineInputBorder(),
                    suffixText: 'ر.ي',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      if (double.tryParse(value) == null) {
                        return 'رقم غير صحيح';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Sale Prices Row
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _wholesalePriceController,
                        decoration: const InputDecoration(
                          labelText: 'سعر الجملة *',
                          border: OutlineInputBorder(),
                          suffixText: 'ر.ي',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (double.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _retailPriceController,
                        decoration: const InputDecoration(
                          labelText: 'سعر التجزئة *',
                          border: OutlineInputBorder(),
                          suffixText: 'ر.ي',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (double.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Quantities Row
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _warehouseQuantityController,
                        decoration: const InputDecoration(
                          labelText: 'كمية المخزن *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (int.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _storeQuantityController,
                        decoration: const InputDecoration(
                          labelText: 'كمية المحل *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (int.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Min Stock and Barcode Row
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _minStockController,
                        decoration: const InputDecoration(
                          labelText: 'الحد الأدنى للمخزون *',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'مطلوب';
                          }
                          if (int.tryParse(value) == null) {
                            return 'رقم غير صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _barcodeController,
                        decoration: const InputDecoration(
                          labelText: 'الباركود',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Save Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveProduct,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : Text(_isEditMode ? 'تحديث المنتج' : 'حفظ المنتج'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // Get context-dependent objects before async operations
    final productProvider = context.read<ProductProvider>();
    final messenger = ScaffoldMessenger.of(context);
    final router = GoRouter.of(context);

    try {
      final product = Product(
        id: _isEditMode ? widget.productId : null,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        category: _selectedCategory!.trim(),
        unit: _unitController.text.trim(),
        lastPurchasePrice: _purchasePriceController.text.trim().isEmpty
            ? null
            : double.parse(_purchasePriceController.text),
        wholesalePrice: double.parse(_wholesalePriceController.text),
        retailPrice: double.parse(_retailPriceController.text),
        minStockQuantity: int.parse(_minStockController.text),
        barcode: _barcodeController.text.trim().isEmpty
            ? null
            : _barcodeController.text.trim(),
        warehouseQuantity: int.parse(_warehouseQuantityController.text),
        storeQuantity: int.parse(_storeQuantityController.text),
      );

      final success = _isEditMode
          ? await productProvider.updateProduct(product)
          : await productProvider.addProduct(product);

      if (success && mounted) {
        messenger.showSnackBar(
          SnackBar(
            content: Text(
              _isEditMode ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح',
            ),
          ),
        );
        router.go('/products');
      } else if (mounted) {
        final errorMessage = productProvider.errorMessage;
        messenger.showSnackBar(
          SnackBar(
            content: Text(errorMessage ?? 'حدث خطأ أثناء حفظ المنتج'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        messenger.showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
