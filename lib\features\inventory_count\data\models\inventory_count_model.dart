class InventoryCountModel {
  final int? id;
  final int productId;
  final DateTime countDate;
  final int countedWarehouseQuantity;
  final int countedStoreQuantity;
  final int systemWarehouseQuantity;
  final int systemStoreQuantity;
  final int warehouseDifference;
  final int storeDifference;
  final String? notes;

  const InventoryCountModel({
    this.id,
    required this.productId,
    required this.countDate,
    required this.countedWarehouseQuantity,
    required this.countedStoreQuantity,
    required this.systemWarehouseQuantity,
    required this.systemStoreQuantity,
    required this.warehouseDifference,
    required this.storeDifference,
    this.notes,
  });

  // Convert from Map (from database)
  factory InventoryCountModel.fromMap(Map<String, dynamic> map) {
    return InventoryCountModel(
      id: map['id'] as int?,
      productId: map['productId'] as int,
      countDate: DateTime.parse(map['countDate'] as String),
      countedWarehouseQuantity: map['countedWarehouseQuantity'] as int,
      countedStoreQuantity: map['countedStoreQuantity'] as int,
      systemWarehouseQuantity: map['systemWarehouseQuantity'] as int,
      systemStoreQuantity: map['systemStoreQuantity'] as int,
      warehouseDifference: map['warehouseDifference'] as int,
      storeDifference: map['storeDifference'] as int,
      notes: map['notes'] as String?,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'countDate': countDate.toIso8601String(),
      'countedWarehouseQuantity': countedWarehouseQuantity,
      'countedStoreQuantity': countedStoreQuantity,
      'systemWarehouseQuantity': systemWarehouseQuantity,
      'systemStoreQuantity': systemStoreQuantity,
      'warehouseDifference': warehouseDifference,
      'storeDifference': storeDifference,
      'notes': notes,
    };
  }

  // Business logic methods
  bool get hasWarehouseDifference => warehouseDifference != 0;
  bool get hasStoreDifference => storeDifference != 0;
  bool get hasAnyDifference => hasWarehouseDifference || hasStoreDifference;
  
  int get totalSystemQuantity => systemWarehouseQuantity + systemStoreQuantity;
  int get totalCountedQuantity => countedWarehouseQuantity + countedStoreQuantity;
  int get totalDifference => totalCountedQuantity - totalSystemQuantity;

  String get warehouseDifferenceText {
    if (warehouseDifference > 0) {
      return '+$warehouseDifference (زيادة)';
    } else if (warehouseDifference < 0) {
      return '$warehouseDifference (نقص)';
    } else {
      return 'لا يوجد فرق';
    }
  }

  String get storeDifferenceText {
    if (storeDifference > 0) {
      return '+$storeDifference (زيادة)';
    } else if (storeDifference < 0) {
      return '$storeDifference (نقص)';
    } else {
      return 'لا يوجد فرق';
    }
  }

  // Copy with method for updates
  InventoryCountModel copyWith({
    int? id,
    int? productId,
    DateTime? countDate,
    int? countedWarehouseQuantity,
    int? countedStoreQuantity,
    int? systemWarehouseQuantity,
    int? systemStoreQuantity,
    int? warehouseDifference,
    int? storeDifference,
    String? notes,
  }) {
    return InventoryCountModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      countDate: countDate ?? this.countDate,
      countedWarehouseQuantity: countedWarehouseQuantity ?? this.countedWarehouseQuantity,
      countedStoreQuantity: countedStoreQuantity ?? this.countedStoreQuantity,
      systemWarehouseQuantity: systemWarehouseQuantity ?? this.systemWarehouseQuantity,
      systemStoreQuantity: systemStoreQuantity ?? this.systemStoreQuantity,
      warehouseDifference: warehouseDifference ?? this.warehouseDifference,
      storeDifference: storeDifference ?? this.storeDifference,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'InventoryCountModel(id: $id, productId: $productId, countDate: $countDate, '
        'countedWarehouseQuantity: $countedWarehouseQuantity, countedStoreQuantity: $countedStoreQuantity, '
        'systemWarehouseQuantity: $systemWarehouseQuantity, systemStoreQuantity: $systemStoreQuantity, '
        'warehouseDifference: $warehouseDifference, storeDifference: $storeDifference, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryCountModel &&
        other.id == id &&
        other.productId == productId &&
        other.countDate == countDate &&
        other.countedWarehouseQuantity == countedWarehouseQuantity &&
        other.countedStoreQuantity == countedStoreQuantity &&
        other.systemWarehouseQuantity == systemWarehouseQuantity &&
        other.systemStoreQuantity == systemStoreQuantity &&
        other.warehouseDifference == warehouseDifference &&
        other.storeDifference == storeDifference &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productId.hashCode ^
        countDate.hashCode ^
        countedWarehouseQuantity.hashCode ^
        countedStoreQuantity.hashCode ^
        systemWarehouseQuantity.hashCode ^
        systemStoreQuantity.hashCode ^
        warehouseDifference.hashCode ^
        storeDifference.hashCode ^
        notes.hashCode;
  }
}
