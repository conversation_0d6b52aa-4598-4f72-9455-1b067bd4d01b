import 'dart:io';
import 'package:flutter/material.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/entities/purchase_batch.dart';
import 'package:market/features/products/domain/usecases/get_all_products.dart';
import 'package:market/features/products/domain/usecases/create_product.dart';
import 'package:market/features/products/domain/usecases/update_product.dart';
import 'package:market/features/products/domain/usecases/delete_product.dart';
import 'package:market/features/products/domain/usecases/get_product_by_id.dart';
import 'package:market/features/products/domain/usecases/add_purchase_batch.dart';
import 'package:market/features/products/domain/usecases/get_oldest_purchase_batches.dart';
import 'package:market/features/products/domain/usecases/update_purchase_batch_quantity.dart';
import 'package:market/utils/excel_importer.dart';

class ProductProvider extends ChangeNotifier {
  final GetAllProductsUseCase _getAllProductsUseCase;
  final CreateProductUseCase _createProductUseCase;
  final UpdateProductUseCase _updateProductUseCase;
  final DeleteProductUseCase _deleteProductUseCase;
  final GetProductByIdUseCase _getProductByIdUseCase;

  // Purchase Batch Use Cases
  final AddPurchaseBatchUseCase _addPurchaseBatchUseCase;
  final GetOldestPurchaseBatchesUseCase _getOldestPurchaseBatchesUseCase;
  final UpdatePurchaseBatchQuantityUseCase _updatePurchaseBatchQuantityUseCase;

  ProductProvider(
    this._getAllProductsUseCase,
    this._createProductUseCase,
    this._updateProductUseCase,
    this._deleteProductUseCase,
    this._getProductByIdUseCase,
    this._addPurchaseBatchUseCase,
    this._getOldestPurchaseBatchesUseCase,
    this._updatePurchaseBatchQuantityUseCase,
  );

  // State variables
  List<Product> _products = [];
  bool _isLoading = false;
  String? _errorMessage;
  Product? _selectedProduct;
  String? _selectedCategory; // للفلترة حسب الفئة

  // Getters
  List<Product> get products => _products;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Product? get selectedProduct => _selectedProduct;
  String? get selectedCategory => _selectedCategory;

  // Fetch all products
  Future<void> fetchProducts() async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _getAllProductsUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المنتجات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Add new product
  Future<bool> addProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      await _createProductUseCase.call(product);
      await fetchProducts(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to add product: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update existing product
  Future<bool> updateProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateProductUseCase.call(product);
      await fetchProducts(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to update product: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete product
  Future<bool> deleteProduct(int productId) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteProductUseCase.call(productId);
      await fetchProducts(); // Refresh the list
      return true;
    } catch (e) {
      _setError('Failed to delete product: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get product by ID
  Future<void> getProductById(int productId) async {
    _setLoading(true);
    _clearError();

    try {
      _selectedProduct = await _getProductByIdUseCase.call(productId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get product: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Clear selected product
  void clearSelectedProduct() {
    _selectedProduct = null;
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Search products
  List<Product> searchProducts(String query) {
    if (query.isEmpty) return _products;

    return _products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.category.toLowerCase().contains(query.toLowerCase()) ||
          (product.barcode?.toLowerCase().contains(query.toLowerCase()) ??
              false);
    }).toList();
  }

  // Get products by category
  List<Product> getProductsByCategory(String category) {
    return _products.where((product) => product.category == category).toList();
  }

  // Get low stock products
  List<Product> getLowStockProducts() {
    return _products.where((product) => product.isLowStock).toList();
  }

  // Filter products by category
  void filterProductsByCategory(String? categoryName) {
    _selectedCategory = categoryName;
    notifyListeners();
  }

  // Get filtered products (by category and search)
  List<Product> getFilteredProducts(String searchQuery) {
    List<Product> filteredProducts = _products;

    // Apply category filter
    if (_selectedCategory != null && _selectedCategory!.isNotEmpty) {
      filteredProducts = filteredProducts
          .where((product) => product.category == _selectedCategory)
          .toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredProducts = filteredProducts.where((product) {
        return product.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
            product.category.toLowerCase().contains(
              searchQuery.toLowerCase(),
            ) ||
            (product.barcode?.toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ) ??
                false);
      }).toList();
    }

    return filteredProducts;
  }

  /// Import products from Excel file
  Future<ImportResult> importProducts(File file) async {
    _setLoading(true);
    _clearError();

    try {
      final importedProducts = await ExcelImporter.importProducts(file);

      int successCount = 0;
      int errorCount = 0;
      List<String> errors = [];

      for (final product in importedProducts) {
        try {
          final success = await addProduct(product);
          if (success) {
            successCount++;
          } else {
            errorCount++;
            errors.add('فشل في إضافة المنتج: ${product.name}');
          }
        } catch (e) {
          errorCount++;
          errors.add('خطأ في المنتج ${product.name}: $e');
        }
      }

      return ImportResult(
        totalCount: importedProducts.length,
        successCount: successCount,
        errorCount: errorCount,
        errors: errors,
      );
    } catch (e) {
      _setError('فشل في استيراد المنتجات: ${e.toString()}');
      return ImportResult(
        totalCount: 0,
        successCount: 0,
        errorCount: 1,
        errors: [e.toString()],
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Calculate cost for a given quantity using FIFO method
  /// This method deducts quantity from oldest batches and returns total cost
  Future<double> getCostForQuantity(int productId, int quantity) async {
    if (quantity <= 0) {
      throw Exception('Quantity must be greater than 0');
    }

    try {
      // Get oldest active batches for the product
      final batches = await _getOldestPurchaseBatchesUseCase.call(productId);

      if (batches.isEmpty) {
        debugPrint(
          'Warning: No purchase batches available for product $productId. Cost set to 0.0',
        );
        return 0.0;
      }

      double totalCost = 0.0;
      int remainingQuantityToDeduct = quantity;

      // Process batches in FIFO order (oldest first)
      for (final batch in batches) {
        if (remainingQuantityToDeduct <= 0) break;

        // Calculate how much we can deduct from this batch
        final quantityFromThisBatch =
            remainingQuantityToDeduct > batch.remainingQuantity
            ? batch.remainingQuantity
            : remainingQuantityToDeduct;

        // Add cost from this batch
        totalCost += quantityFromThisBatch * batch.unitPurchasePrice;

        // Update remaining quantity in this batch
        final newRemainingQuantity =
            batch.remainingQuantity - quantityFromThisBatch;
        await _updatePurchaseBatchQuantityUseCase.call(
          batch.id!,
          newRemainingQuantity,
        );

        // Update remaining quantity to deduct
        remainingQuantityToDeduct -= quantityFromThisBatch;
      }

      // Check if we couldn't fulfill the full quantity
      if (remainingQuantityToDeduct > 0) {
        debugPrint(
          'Warning: Insufficient quantity in batches for product $productId. Missing: $remainingQuantityToDeduct units. Partial cost calculated.',
        );
        // Return partial cost instead of throwing exception
      }

      return totalCost;
    } catch (e) {
      throw Exception('فشل في حساب التكلفة للكمية: $e');
    }
  }

  /// Central method for updating product quantities and managing batches
  /// Handles both purchases (adding batches) and sales (deducting from batches)
  Future<void> updateProductQuantitiesAndBatches(
    int productId, {
    int? warehouseDecrement,
    int? storeDecrement,
    int? warehouseIncrement,
    int? storeIncrement,
    double? newLastPurchasePrice,
    int? newBatchQuantity,
    double? newBatchUnitPrice,
    DateTime? newBatchPurchaseDate,
  }) async {
    try {
      // Get current product
      final currentProduct = await _getProductByIdUseCase.call(productId);
      if (currentProduct == null) {
        throw Exception('Product not found with id: $productId');
      }

      // Handle new purchase (adding batch)
      if (newBatchQuantity != null &&
          newBatchUnitPrice != null &&
          newBatchPurchaseDate != null) {
        // Add new purchase batch
        final newBatch = PurchaseBatch(
          productId: productId,
          purchaseDate: newBatchPurchaseDate,
          quantity: newBatchQuantity,
          unitPurchasePrice: newBatchUnitPrice,
          remainingQuantity: newBatchQuantity,
          isActive: true,
        );

        await _addPurchaseBatchUseCase.call(newBatch);
      }

      // Calculate new quantities
      int newWarehouseQuantity = currentProduct.warehouseQuantity;
      int newStoreQuantity = currentProduct.storeQuantity;

      // Handle decrements (sales/transfers)
      if (warehouseDecrement != null && warehouseDecrement > 0) {
        newWarehouseQuantity -= warehouseDecrement;
        // Calculate cost using FIFO
        await getCostForQuantity(productId, warehouseDecrement);
      }

      if (storeDecrement != null && storeDecrement > 0) {
        newStoreQuantity -= storeDecrement;
        // Calculate cost using FIFO
        await getCostForQuantity(productId, storeDecrement);
      }

      // Handle increments (purchases/transfers)
      if (warehouseIncrement != null && warehouseIncrement > 0) {
        newWarehouseQuantity += warehouseIncrement;
      }

      if (storeIncrement != null && storeIncrement > 0) {
        newStoreQuantity += storeIncrement;
      }

      // Validate quantities
      if (newWarehouseQuantity < 0) {
        throw Exception('Insufficient warehouse quantity');
      }
      if (newStoreQuantity < 0) {
        throw Exception('Insufficient store quantity');
      }

      // Update product with new quantities and last purchase price
      final updatedProduct = currentProduct.copyWith(
        warehouseQuantity: newWarehouseQuantity,
        storeQuantity: newStoreQuantity,
        lastPurchasePrice:
            newLastPurchasePrice ?? currentProduct.lastPurchasePrice,
      );

      await _updateProductUseCase.call(updatedProduct);

      // Refresh products list
      await fetchProducts();
    } catch (e) {
      throw Exception('Failed to update product quantities and batches: $e');
    }
  }

  // Simplified method for sales and purchases
  Future<bool> updateProductQuantitiesForSalesAndPurchases(
    int productId,
    int quantity, {
    bool isDecrease = false,
    double? newPurchasePrice,
  }) async {
    try {
      if (isDecrease) {
        // For sales - decrease warehouse quantity
        await updateProductQuantitiesAndBatches(
          productId,
          warehouseDecrement: quantity,
        );
      } else {
        // For purchases - increase warehouse quantity and add batch
        await updateProductQuantitiesAndBatches(
          productId,
          warehouseIncrement: quantity,
          newBatchQuantity: quantity,
          newBatchUnitPrice: newPurchasePrice ?? 0.0,
          newBatchPurchaseDate: DateTime.now(),
          newLastPurchasePrice: newPurchasePrice,
        );
      }
      return true;
    } catch (e) {
      _setError('Failed to update product quantities: ${e.toString()}');
      return false;
    }
  }
}

class ImportResult {
  final int totalCount;
  final int successCount;
  final int errorCount;
  final List<String> errors;

  ImportResult({
    required this.totalCount,
    required this.successCount,
    required this.errorCount,
    required this.errors,
  });

  bool get hasErrors => errorCount > 0;
  bool get isSuccess => successCount > 0;

  String get summary {
    if (totalCount == 0) return 'لم يتم العثور على بيانات للاستيراد';
    if (errorCount == 0) {
      return 'تم استيراد جميع البيانات بنجاح ($successCount عنصر)';
    }
    if (successCount == 0) {
      return 'فشل في استيراد جميع البيانات ($errorCount خطأ)';
    }
    return 'تم استيراد $successCount من أصل $totalCount عنصر ($errorCount خطأ)';
  }
}
