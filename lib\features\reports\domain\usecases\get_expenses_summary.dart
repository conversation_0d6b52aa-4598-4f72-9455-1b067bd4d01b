import '../repositories/analytics_repository.dart';

class GetExpensesSummaryUseCase {
  final AnalyticsRepository _repository;

  GetExpensesSummaryUseCase(this._repository);

  Future<Map<String, dynamic>> call({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    // Validation
    if (fromDate != null && toDate != null && fromDate.isAfter(toDate)) {
      throw Exception('From date cannot be after to date');
    }

    try {
      // Get purchases summary as expenses
      final purchasesSummary = await _repository.getPurchasesSummary(
        fromDate: fromDate,
        toDate: toDate,
      );

      // Get customer analytics for outstanding amounts
      final customerAnalytics = await _repository.getCustomerAnalytics();

      return {
        'totalPurchases': purchasesSummary['totalCost'] ?? 0.0,
        'totalOutstanding': customerAnalytics['totalOutstanding'] ?? 0.0,
        'purchaseBreakdown': purchasesSummary['paymentMethods'] ?? [],
      };
    } catch (e) {
      throw Exception('Failed to get expenses summary: $e');
    }
  }
}
