import '../../data/models/sale_item_model.dart';

class SaleItem {
  final int? id;
  final int saleId;
  final int? productId;
  final int? quantity;
  final double unitPrice;
  final String itemType; // 'wholesale_product' or 'retail_goods_summary'
  final String? description;

  const SaleItem({
    this.id,
    required this.saleId,
    this.productId,
    this.quantity,
    required this.unitPrice,
    required this.itemType,
    this.description,
  });

  // Create SaleItem from SaleItemModel
  factory SaleItem.fromModel(SaleItemModel model) {
    return SaleItem(
      id: model.id,
      saleId: model.saleId,
      productId: model.productId,
      quantity: model.quantity,
      unitPrice: model.unitPrice,
      itemType: model.itemType,
      description: model.description,
    );
  }

  // Business logic methods
  double get totalPrice => (quantity ?? 1) * unitPrice;

  // تحديد نوع البند - المنتجات العادية من المخزون
  bool get isWholesaleProduct => itemType == 'product';
  // بنود الخدمات أو ملخصات البضاعة التي لا تؤثر على المخزون
  bool get isRetailGoodsSummary => itemType == 'service';

  String get itemTypeDisplayName {
    switch (itemType) {
      case 'product':
        return 'منتج من المخزون';
      case 'service':
        return 'خدمة أو بضاعة متنوعة';
      default:
        return itemType;
    }
  }

  String get displayDescription {
    if (description != null && description!.isNotEmpty) {
      return description!;
    }
    if (isWholesaleProduct && productId != null) {
      return 'منتج رقم $productId';
    }
    return 'بند غير محدد';
  }

  // Copy with method for updates
  SaleItem copyWith({
    int? id,
    int? saleId,
    int? productId,
    int? quantity,
    double? unitPrice,
    String? itemType,
    String? description,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      itemType: itemType ?? this.itemType,
      description: description ?? this.description,
    );
  }

  @override
  String toString() {
    return 'SaleItem(id: $id, saleId: $saleId, productId: $productId, '
        'quantity: $quantity, unitPrice: $unitPrice, itemType: $itemType, '
        'description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SaleItem &&
        other.id == id &&
        other.saleId == saleId &&
        other.productId == productId &&
        other.quantity == quantity &&
        other.unitPrice == unitPrice &&
        other.itemType == itemType &&
        other.description == description;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        saleId.hashCode ^
        productId.hashCode ^
        quantity.hashCode ^
        unitPrice.hashCode ^
        itemType.hashCode ^
        description.hashCode;
  }
}
