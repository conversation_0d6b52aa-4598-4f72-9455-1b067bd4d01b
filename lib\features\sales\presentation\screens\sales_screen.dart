import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/shared_widgets/custom_app_bar.dart';
import '../providers/sale_provider.dart';
import '../../domain/entities/sale.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import 'package:intl/intl.dart';

class SalesScreen extends StatefulWidget {
  const SalesScreen({super.key});

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load sales when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SaleProvider>().fetchSales();
      context.read<CustomerProvider>().loadCustomers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainScreenWrapper(
      title: 'المبيعات',
      customAppBar: CustomAppBar(
        title: 'المبيعات',
        showBackButton: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
            tooltip: 'فلترة',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/sales/new'),
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: Consumer<SaleProvider>(
        builder: (context, saleProvider, child) {
          if (saleProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (saleProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المبيعات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    saleProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => saleProvider.fetchSales(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (saleProvider.sales.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا يوجد مبيعات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على زر الإضافة لإضافة فاتورة مبيعات جديدة',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في المبيعات...',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              // Sales list
              Expanded(
                child: Builder(
                  builder: (context) {
                    final filteredSales = saleProvider.getFilteredSales(
                      _searchQuery,
                    );

                    if (filteredSales.isEmpty && _searchQuery.isNotEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج للبحث',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'جرب البحث بكلمات مختلفة',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        await saleProvider.fetchSales();
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: filteredSales.length,
                        itemBuilder: (context, index) {
                          final sale = filteredSales[index];
                          return _buildSaleCard(context, sale);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final saleProvider = context.read<SaleProvider>();
    final customerProvider = context.read<CustomerProvider>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة المبيعات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Status Filter
              const Text(
                'حالة الدفع:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String?>(
                value: saleProvider.selectedPaymentStatus,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('الكل')),
                  DropdownMenuItem(
                    value: 'مدفوعة بالكامل',
                    child: Text('مدفوعة بالكامل'),
                  ),
                  DropdownMenuItem(
                    value: 'مدفوعة جزئياً',
                    child: Text('مدفوعة جزئياً'),
                  ),
                  DropdownMenuItem(
                    value: 'غير مدفوعة',
                    child: Text('غير مدفوعة'),
                  ),
                ],
                onChanged: (value) {
                  saleProvider.filterSales(paymentStatus: value);
                },
              ),
              const SizedBox(height: 16),

              // Payment Method Filter
              const Text(
                'طريقة الدفع:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String?>(
                value: saleProvider.selectedPaymentMethod,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('الكل')),
                  DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                  DropdownMenuItem(value: 'credit', child: Text('آجل')),
                  DropdownMenuItem(value: 'card', child: Text('بطاقة ائتمان')),
                  DropdownMenuItem(
                    value: 'transfer',
                    child: Text('تحويل بنكي'),
                  ),
                ],
                onChanged: (value) {
                  saleProvider.filterSales(paymentMethod: value);
                },
              ),
              const SizedBox(height: 16),

              // Customer Filter
              const Text(
                'العميل:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<int?>(
                value: saleProvider.selectedCustomerId,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: [
                  const DropdownMenuItem(value: null, child: Text('الكل')),
                  ...customerProvider.customers.map((customer) {
                    return DropdownMenuItem(
                      value: customer.id,
                      child: Text(customer.name),
                    );
                  }),
                ],
                onChanged: (value) {
                  saleProvider.filterSales(customerId: value);
                },
              ),
              const SizedBox(height: 16),

              // Date Range Filter
              const Text(
                'التاريخ:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextButton.icon(
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              saleProvider.selectedFromDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          saleProvider.filterSales(fromDate: date);
                        }
                      },
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        saleProvider.selectedFromDate != null
                            ? DateFormat(
                                'dd/MM/yyyy',
                              ).format(saleProvider.selectedFromDate!)
                            : 'من تاريخ',
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton.icon(
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              saleProvider.selectedToDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          saleProvider.filterSales(toDate: date);
                        }
                      },
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        saleProvider.selectedToDate != null
                            ? DateFormat(
                                'dd/MM/yyyy',
                              ).format(saleProvider.selectedToDate!)
                            : 'إلى تاريخ',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              saleProvider.clearFilters();
              Navigator.of(context).pop();
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleCard(BuildContext context, Sale sale) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: Colors.green,
          child: Icon(Icons.shopping_cart, color: Colors.white),
        ),
        title: Text(
          'فاتورة رقم ${sale.id}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '👤 ${sale.customerId != null ? "عميل رقم ${sale.customerId}" : "عميل عابر"}',
            ),
            Text('📅 ${dateFormat.format(sale.saleDate)}'),
            Text('💳 ${sale.paymentMethodDisplayName}'),
            Text('📊 ${sale.statusDisplayName}'),
            const SizedBox(height: 8),
            // حالة الدفع مع Chip ملون
            Row(
              children: [
                Chip(
                  label: Text(
                    sale.paymentStatusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  backgroundColor: sale.isFullyPaid
                      ? Colors.green
                      : sale.isPartiallyPaid
                      ? Colors.orange
                      : Colors.red,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                if (sale.dueAmount > 0) ...[
                  const SizedBox(width: 8),
                  Text(
                    'متبقي: ${sale.dueAmount.toStringAsFixed(2)} ر.ي',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: SizedBox(
          width: 120,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${sale.totalAmount.toStringAsFixed(2)} ر.ي',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 4),
              PopupMenuButton<String>(
                padding: EdgeInsets.zero,
                iconSize: 20,
                onSelected: (value) {
                  switch (value) {
                    case 'view':
                      context.go('/sales/view/${sale.id}');
                      break;
                    case 'edit':
                      context.go('/sales/edit/${sale.id}');
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.visibility, size: 16),
                        SizedBox(width: 8),
                        Text('عرض'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        onTap: () => context.go('/sales/view/${sale.id}'),
      ),
    );
  }
}
